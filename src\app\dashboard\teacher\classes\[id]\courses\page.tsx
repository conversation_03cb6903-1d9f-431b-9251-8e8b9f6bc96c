'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Search,
  BookO<PERSON>,
  Plus,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Trash2,
  Eye,
  Calendar,
  Users
} from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

interface ClassData {
  id: number;
  name: string;
  description: string;
  studentCount: number;
  courseCount: number;
}

interface CourseAssignment {
  id: number;
  courseId: number;
  classId: number;
  courseName: string;
  courseCode: string;
  courseDescription: string;
  enrolledAt: string;
  studentCount: number;
  completionRate: number;
}

interface Course {
  id: number;
  name: string;
  courseCode: string;
  description: string;
}

export default function ClassCoursesPage() {
  const router = useRouter();
  const params = useParams();
  const classId = params.id as string;
  
  const [isLoading, setIsLoading] = useState(true);
  const [classData, setClassData] = useState<ClassData | null>(null);
  const [courseAssignments, setCourseAssignments] = useState<CourseAssignment[]>([]);
  const [availableCourses, setAvailableCourses] = useState<Course[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isRemoving, setIsRemoving] = useState<number | null>(null);
  const [showAssignDialog, setShowAssignDialog] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [isAssigning, setIsAssigning] = useState(false);

  useEffect(() => {
    if (classId) {
      fetchData();
    }
  }, [classId]);

  const fetchData = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view class courses');
        router.push('/auth/sign-in');
        return;
      }

      // Fetch class data
      const classResponse = await fetch(`/api/classes/${classId}?teacherId=${user.id}`);
      const classData = await classResponse.json();

      if (classData.success && classData.class) {
        setClassData(classData.class);
      } else {
        toast.error(classData.error || 'Failed to fetch class data');
        router.push('/dashboard/teacher/classes');
        return;
      }

      // Fetch course assignments for this class
      const assignmentsResponse = await fetch(
        `/api/enrollments?type=course&classId=${classId}&teacherId=${user.id}`
      );
      if (assignmentsResponse.ok) {
        const assignmentsData = await assignmentsResponse.json();
        setCourseAssignments(assignmentsData.enrollments || []);
      }

      // Fetch available courses for assignment
      const coursesResponse = await fetch(`/api/courses?teacherId=${user.id}`);
      if (coursesResponse.ok) {
        const coursesData = await coursesResponse.json();
        setAvailableCourses(coursesData.courses || []);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to fetch class courses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveAssignment = async (assignmentId: number) => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to remove course assignments');
        return;
      }

      setIsRemoving(assignmentId);

      const response = await fetch(`/api/enrollments/${assignmentId}?teacherId=${user.id}&type=course`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Course assignment removed successfully!');
        setCourseAssignments(prev => 
          prev.filter(assignment => assignment.id !== assignmentId)
        );
      } else {
        toast.error(data.error || 'Failed to remove course assignment');
      }
    } catch (error) {
      console.error('Error removing assignment:', error);
      toast.error('Failed to remove course assignment');
    } finally {
      setIsRemoving(null);
    }
  };

  const handleAssignCourse = async () => {
    if (!selectedCourse) {
      toast.error('Please select a course');
      return;
    }

    const user = authStorage.getUser();
    if (!user || user.role !== 'teacher') {
      toast.error('Access denied');
      return;
    }

    setIsAssigning(true);
    try {
      const response = await fetch('/api/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: 'course',
          courseId: parseInt(selectedCourse),
          classId: parseInt(classId),
          teacherId: user.id
        }),
      });

      if (response.ok) {
        toast.success('Course assigned to class successfully');
        setShowAssignDialog(false);
        setSelectedCourse('');
        fetchData(); // Refresh data
      } else {
        const error = await response.json();
        toast.error(error.error || 'Failed to assign course');
      }
    } catch (error) {
      console.error('Error assigning course:', error);
      toast.error('An error occurred while assigning course');
    } finally {
      setIsAssigning(false);
    }
  };

  const filteredAssignments = courseAssignments.filter(assignment =>
    assignment.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.courseCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
    assignment.courseDescription.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (isLoading) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center space-x-4'>
          <Skeleton className='h-10 w-20' />
          <div className='space-y-2'>
            <Skeleton className='h-8 w-64' />
            <Skeleton className='h-4 w-96' />
          </div>
        </div>
        <Card>
          <CardHeader>
            <Skeleton className='h-6 w-48' />
            <Skeleton className='h-4 w-64' />
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              <Skeleton className='h-10 w-full' />
              <div className='space-y-2'>
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className='h-16 w-full' />
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!classData) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold'>Class not found</h2>
          <p className='text-muted-foreground mt-2'>The class you&apos;re looking for doesn&apos;t exist.</p>
          <Link href='/dashboard/teacher/classes'>
            <Button className='mt-4'>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to Classes
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href={`/dashboard/teacher/classes/${classId}`}>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            {classData.name} - Assigned Courses
          </h1>
          <p className='text-muted-foreground'>
            Manage courses assigned to this class
          </p>
        </div>
      </div>

      {/* Class Info Card */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center gap-2'>
            <BookOpen className='h-5 w-5' />
            Class Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-blue-600'>
                {classData.studentCount}
              </div>
              <div className='text-sm text-muted-foreground'>Students</div>
            </div>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-green-600'>
                {courseAssignments.length}
              </div>
              <div className='text-sm text-muted-foreground'>Assigned Courses</div>
            </div>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-purple-600'>
                {courseAssignments.length > 0 
                  ? Math.round(courseAssignments.reduce((acc, course) => acc + course.completionRate, 0) / courseAssignments.length)
                  : 0}%
              </div>
              <div className='text-sm text-muted-foreground'>Avg. Completion</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Assigned Courses Card */}
      <Card>
        <CardHeader>
          <div className='flex items-center justify-between'>
            <div>
              <CardTitle>Assigned Courses</CardTitle>
              <CardDescription>
                Courses currently assigned to {classData.name}
              </CardDescription>
            </div>
            <Button onClick={() => setShowAssignDialog(true)}>
              <Plus className='mr-2 h-4 w-4' />
              Assign Course
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {/* Search */}
            <div className='relative'>
              <Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4' />
              <Input
                placeholder='Search assigned courses...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-10'
              />
            </div>

            {/* Courses Table */}
            {filteredAssignments.length > 0 ? (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Course</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>Students</TableHead>
                    <TableHead>Completion</TableHead>
                    <TableHead>Assigned Date</TableHead>
                    <TableHead className='text-right'>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredAssignments.map((assignment) => (
                    <TableRow key={assignment.id}>
                      <TableCell>
                        <div>
                          <div className='font-medium'>{assignment.courseName}</div>
                          <div className='text-sm text-muted-foreground line-clamp-1'>
                            {assignment.courseDescription}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant='outline'>{assignment.courseCode}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center gap-1'>
                          <Users className='h-4 w-4 text-muted-foreground' />
                          {assignment.studentCount}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center gap-2'>
                          <div className='w-16 bg-gray-200 rounded-full h-2'>
                            <div 
                              className='bg-green-600 h-2 rounded-full' 
                              style={{ width: `${assignment.completionRate}%` }}
                            />
                          </div>
                          <span className='text-sm text-muted-foreground'>
                            {assignment.completionRate}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center gap-1 text-sm text-muted-foreground'>
                          <Calendar className='h-4 w-4' />
                          {new Date(assignment.enrolledAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell className='text-right'>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant='ghost' className='h-8 w-8 p-0'>
                              <MoreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/teacher/courses/${assignment.courseId}`}>
                                <Eye className='mr-2 h-4 w-4' />
                                View Course
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className='text-red-600'
                              onClick={() => handleRemoveAssignment(assignment.id)}
                              disabled={isRemoving === assignment.id}
                            >
                              {isRemoving === assignment.id ? (
                                <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent' />
                              ) : (
                                <Trash2 className='mr-2 h-4 w-4' />
                              )}
                              Remove Assignment
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            ) : (
              <div className='text-center py-12'>
                <BookOpen className='mx-auto h-12 w-12 text-muted-foreground mb-4' />
                <h3 className='text-lg font-medium mb-2'>No courses assigned</h3>
                <p className='text-muted-foreground mb-4'>
                  {searchTerm 
                    ? 'No courses match your search criteria.' 
                    : 'This class doesn\'t have any assigned courses yet.'}
                </p>
                {!searchTerm && (
                  <Button onClick={() => setShowAssignDialog(true)}>
                    <Plus className='mr-2 h-4 w-4' />
                    Assign First Course
                  </Button>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Assign Course Dialog */}
      <Dialog open={showAssignDialog} onOpenChange={setShowAssignDialog}>
        <DialogContent className='sm:max-w-[425px]'>
          <DialogHeader>
            <DialogTitle>Assign Course to {classData?.name}</DialogTitle>
            <DialogDescription>
              Select a course to assign to this class.
            </DialogDescription>
          </DialogHeader>
          <div className='grid gap-4 py-4'>
            <div className='space-y-2'>
              <label className='text-sm font-medium'>Course</label>
              <Select value={selectedCourse} onValueChange={setSelectedCourse}>
                <SelectTrigger>
                  <SelectValue placeholder='Select a course' />
                </SelectTrigger>
                <SelectContent>
                  {availableCourses
                    .filter(course => !courseAssignments.some(assignment => assignment.courseId === course.id))
                    .map((course) => (
                    <SelectItem key={course.id} value={course.id.toString()}>
                      <div className='flex flex-col'>
                        <span>{course.name}</span>
                        <span className='text-xs text-muted-foreground'>
                          {course.courseCode}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant='outline'
              onClick={() => {
                setShowAssignDialog(false);
                setSelectedCourse('');
              }}
              disabled={isAssigning}
            >
              Cancel
            </Button>
            <Button onClick={handleAssignCourse} disabled={isAssigning}>
              {isAssigning ? 'Assigning...' : 'Assign Course'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}