'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Users,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  BookOpen,
  Loader2,
  ImageIcon
} from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';
import { Skeleton } from '@/components/ui/skeleton';

interface ClassData {
  id: number;
  name: string;
  description: string;
  studentCount: number;
  courseCount: number;
  createdAt: string;
  status: string;
  coverPicture?: string;
}

// Skeleton loading component
const ClassSkeleton = () => (
  <Card>
    <CardHeader>
      <Skeleton className='h-48 w-full rounded-lg mb-4' />
      <Skeleton className='h-6 w-3/4 mb-2' />
      <Skeleton className='h-4 w-full mb-1' />
      <Skeleton className='h-4 w-2/3' />
    </CardHeader>
    <CardContent>
      <div className='flex justify-between mb-4'>
        <div className='space-y-2'>
          <Skeleton className='h-4 w-20' />
          <Skeleton className='h-4 w-24' />
        </div>
        <div className='space-y-2'>
          <Skeleton className='h-4 w-20' />
          <Skeleton className='h-4 w-24' />
        </div>
      </div>
      <div className='flex justify-between items-center'>
        <Skeleton className='h-6 w-20' />
        <Skeleton className='h-4 w-32' />
      </div>
    </CardContent>
    <CardFooter className='flex justify-end gap-3'>
      <Skeleton className='h-9 w-16' />
      <Skeleton className='h-9 w-16' />
    </CardFooter>
  </Card>
);

export default function ClassesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [classes, setClasses] = useState<ClassData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<number | null>(null);

  useEffect(() => {
    fetchClasses();
  }, []);

  const fetchClasses = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view classes');
        return;
      }

      const response = await fetch(`/api/classes?teacherId=${user.id}`);
      const data = await response.json();

      if (data.success) {
        setClasses(data.classes || []);
      } else {
        toast.error(data.error || 'Failed to fetch classes');
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('Failed to fetch classes');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClass = async (classId: number) => {
    if (!confirm('Are you sure you want to delete this class? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(classId);
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to delete classes');
        return;
      }

      const response = await fetch(`/api/classes/${classId}?teacherId=${user.id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        toast.success('Class deleted successfully');
        fetchClasses(); // Refresh the list
      } else {
        toast.error(data.error || 'Failed to delete class');
      }
    } catch (error) {
      console.error('Error deleting class:', error);
      toast.error('Failed to delete class');
    } finally {
      setIsDeleting(null);
    }
  };

  const filteredClasses = classes.filter(
    (classItem) =>
      classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classItem.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Classes</h1>
          <p className='text-muted-foreground'>
            Manage your classes and student groups
          </p>
        </div>
        <Link href='/dashboard/teacher/classes/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Create Class
          </Button>
        </Link>
      </div>
      <div className='flex items-center space-x-2'>
        <div className='relative flex-1'>
          <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
          <Input
            placeholder='Search classes...'
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className='pl-8'
          />
        </div>
      </div>
      <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
        {isLoading ? (
          // Show loading skeletons
          Array.from({ length: 6 }).map((_, index) => (
            <ClassSkeleton key={index} />
          ))
        ) : filteredClasses.length === 0 ? (
          <div className='col-span-full flex flex-col items-center justify-center py-12'>
            <div className='text-6xl mb-4'>👩‍🏫</div>
            <h3 className='mt-2 text-xl font-semibold'>No classes found</h3>
            <p className='text-muted-foreground mt-1 text-sm'>
              {searchTerm
                ? 'Try adjusting your search terms.'
                : 'Get started by creating a new class.'}
            </p>
            {!searchTerm && (
              <div className='mt-6'>
                <Link href='/dashboard/teacher/classes/new'>
                  <Button>
                    <Plus className='mr-2 h-4 w-4' />
                    Create Class
                  </Button>
                </Link>
              </div>
            )}
          </div>
        ) : (
          filteredClasses.map((classItem) => (
            <Card key={classItem.id} className='relative overflow-hidden animate-fade-in w-full'>
              <CardHeader className='p-0'>
                {/* Cover Image Section */}
                <div className="p-6 pb-0">
                  <div className="h-48 w-full overflow-hidden rounded-lg">
                    {classItem.coverPicture ? (
                      <img
                        src={classItem.coverPicture}
                        alt={`Cover for ${classItem.name}`}
                        loading='lazy'
                        className='h-full w-full object-cover'
                      />
                    ) : (
                      <div className='h-full w-full bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center'>
                        <ImageIcon className='h-16 w-16 text-gray-400' />
                      </div>
                    )}
                  </div>
                </div>
                {/* Text and Dropdown Menu */}
                <div className='p-6'>
                  <div className='flex items-start justify-between'>
                    <div className='space-y-1 flex-1'>
                      <CardTitle className='text-xl'>{classItem.name}</CardTitle>
                      <CardDescription className='text-sm'>
                        {classItem.description.substring(0, 100)}{classItem.description.length > 100 ? '...' : ''}
                      </CardDescription>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant='ghost' className='h-8 w-8 p-0'>
                          <MoreHorizontal className='h-4 w-4' />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align='end'>
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/teacher/classes/${classItem.id}`}>
                            <Edit className='mr-2 h-4 w-4' />
                            Edit
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/dashboard/teacher/classes/${classItem.id}/students`}>
                            <Users className='mr-2 h-4 w-4' />
                            Manage Students
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          className='text-red-600'
                          onClick={() => handleDeleteClass(classItem.id)}
                          disabled={isDeleting === classItem.id}
                        >
                          {isDeleting === classItem.id ? (
                            <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                          ) : (
                            <Trash2 className='mr-2 h-4 w-4' />
                          )}
                          {isDeleting === classItem.id ? 'Deleting...' : 'Delete'}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardHeader>
              <CardContent className='px-6 pt-5 pb-4 space-y-2 border-t'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-2'>
                    <Users className='text-muted-foreground h-4 w-4' />
                    <span className='text-sm font-medium'>{classItem.studentCount}</span>
                    <span className='text-muted-foreground text-sm'>Students</span>
                  </div>
                  <div className='flex items-center space-x-2'>
                    <BookOpen className='text-muted-foreground h-4 w-4' />
                    <span className='text-sm font-medium'>{classItem.courseCount}</span>
                    <span className='text-muted-foreground text-sm'>Courses</span>
                  </div>
                </div>
              </CardContent>
              <CardFooter className='flex justify-end'>
                <Link href={`/dashboard/teacher/classes/${classItem.id}`}>
                  <Button>View Class</Button>
                </Link>
              </CardFooter>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}