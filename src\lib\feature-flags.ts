export interface FeatureFlags {
  enableCoursePurchase: boolean;
  enableEnrollmentCode: boolean;
  enableCoursePreview: boolean;
  enablePaymentIntegration: boolean;
}

const defaultFlags: FeatureFlags = {
  enableCoursePurchase: true,
  enableEnrollmentCode: true,
  enableCoursePreview: true,
  enablePaymentIntegration: false, // Set to true when payment system is ready
};

export function getFeatureFlags(): FeatureFlags {
  if (typeof window === 'undefined') {
    return defaultFlags;
  }

  try {
    const stored = localStorage.getItem('feature-flags');
    if (stored) {
      return { ...defaultFlags, ...JSON.parse(stored) };
    }
  } catch (error) {
    console.warn('Failed to parse feature flags from localStorage:', error);
  }

  return defaultFlags;
}

export function setFeatureFlag(flag: keyof FeatureFlags, value: boolean): void {
  if (typeof window === 'undefined') return;

  try {
    const current = getFeatureFlags();
    const updated = { ...current, [flag]: value };
    localStorage.setItem('feature-flags', JSON.stringify(updated));
  } catch (error) {
    console.warn('Failed to save feature flags to localStorage:', error);
  }
}

export function useFeatureFlags() {
  const flags = getFeatureFlags();
  
  return {
    flags,
    setFlag: setFeatureFlag,
    canPurchase: flags.enableCoursePurchase,
    canEnrollWithCode: flags.enableEnrollmentCode,
    canPreviewCourse: flags.enableCoursePreview,
    hasPaymentIntegration: flags.enablePaymentIntegration,
  };
}