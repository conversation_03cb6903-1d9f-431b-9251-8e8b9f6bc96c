import { generateWithGeminiStructured, SchemaConfig, Type, getChatSession, sendStructuredChatMessage, ChatMessage } from './client';
import type { AIGeneratedContent, AIChapterOutline, AIModuleOutline, AICourseOutline } from './types';
import { AIGenerationError } from './types';

type OptionBlock = {
  id: number;
  isCorrect: boolean;
  content: { id: number; type: 'text'; value: string }[];
};


// Schema for structured output
const contentSchema: SchemaConfig = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: Type.OBJECT,
    properties: {
      content: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            id: { type: Type.NUMBER },
            type: { type: Type.STRING, enum: ['text'] },
            value: { type: Type.STRING, description: "Markdown content" }
          },
          required: ["id", "type", "value"]
        }
      },
      quiz: {
        type: Type.OBJECT,
        properties: {
          name: { type: Type.STRING },
          description: { type: Type.STRING },
          timeLimit: { type: Type.NUMBER },
          minimumScore: { type: Type.NUMBER },
          questions: {
            type: Type.ARRAY,
            items: {
              type: Type.OBJECT,
              properties: {
                orderIndex: { type: Type.NUMBER },
                type: {
                  type: Type.STRING,
                  enum: ["multiple_choice", "true_false", "essay"]
                },
                question: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      id: { type: Type.NUMBER },
                      type: { type: Type.STRING, enum: ['text']},
                      value: { type: Type.STRING }
                    },
                    required: ["id", "type", "value"]
                  }
                },
                options: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      id: { type: Type.NUMBER },
                      isCorrect: { type: Type.BOOLEAN },
                      content: {
                        type: Type.ARRAY,
                        items: {
                          type: Type.OBJECT,
                          properties: {
                            id: { type: Type.NUMBER },
                            type: { type: Type.STRING, enum: ['text']},
                            value: { type: Type.STRING }
                          },
                          required: ["id", "type", "value"]
                        }
                      }
                    },
                    required: ["id", "isCorrect", "content"]
                  }
                },
                essayAnswer: { type: Type.STRING },
                explanation: {
                  type: Type.ARRAY,
                  items: {
                    type: Type.OBJECT,
                    properties: {
                      id: { type: Type.NUMBER },
                      type: { type: Type.STRING, enum: ['text']},
                      value: { type: Type.STRING }
                    },
                    required: ["id", "type", "value"]
                  }
                },
                points: { type: Type.NUMBER }
              },
              required: ["orderIndex", "type", "question", "points", "essayAnswer", "explanation"]
            }
          }
        },
        required: ["name", "description", "timeLimit", "minimumScore", "questions"]
      }
    },
    required: ["content", "quiz"]
  }
};

/**
 * Generate content for a specific chapter with chat session
 * @param chapterOutline Chapter outline to generate content for
 * @param moduleContext Context from the module
 * @param courseContext Context from the entire course
 * @param sessionId Chat session ID for maintaining context
 * @returns Promise<AIGeneratedContent> Generated chapter content
 */
export const generateChapterContent = async (
  chapterOutline: AIChapterOutline,
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline,
  sessionId: string
): Promise<AIGeneratedContent> => {
  try {
    const prompt = createChapterContentPrompt(chapterOutline, moduleContext, courseContext);
    
    // Create or get existing chat session
    getChatSession(sessionId);
    
    // Prepare message
    const message: ChatMessage = {
      role: 'user',
      parts: [{ text: prompt }]
    };

    const generatedContent = await sendStructuredChatMessage<AIGeneratedContent>(sessionId, message, contentSchema);
    
    // Validate the generated content
    validateGeneratedContent(generatedContent, chapterOutline.hasQuiz);
    
    return generatedContent;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate content for chapter: ${chapterOutline.name}`,
      'CONTENT_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate content for a specific chapter (backward compatibility)
 * @param chapterOutline Chapter outline to generate content for
 * @param moduleContext Context from the module
 * @param courseContext Context from the entire course
 * @returns Promise<AIGeneratedContent> Generated chapter content
 */
export const generateChapterContentLegacy = async (
  chapterOutline: AIChapterOutline,
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline
): Promise<AIGeneratedContent> => {
  try {
    const prompt = createChapterContentPrompt(chapterOutline, moduleContext, courseContext);
    
    const contents = [
      {
        role: 'user',
        parts: [{ text: prompt }]
      }
    ];

    const generatedContent = await generateWithGeminiStructured<AIGeneratedContent>(contents, contentSchema);
    
    // Validate the generated content
    validateGeneratedContent(generatedContent, chapterOutline.hasQuiz);
    
    return generatedContent;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      `Failed to generate content for chapter: ${chapterOutline.name}`,
      'CONTENT_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Create prompt for chapter content generation
 * @param chapterOutline Chapter outline
 * @param moduleContext Module context
 * @param courseContext Course context
 * @returns Formatted prompt string
 */
export const createChapterContentPrompt = (
  chapterOutline: AIChapterOutline,
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline
): string => {
  const basePrompt = `
Generate comprehensive educational content for a chapter in an online course using the PDF content that i sent before.

Course Context:
- Course: "${courseContext.courseName}"
- Course Description: ${courseContext.description}

Module Context:
- Module: "${moduleContext.name}"
- Module Description: ${moduleContext.description}

Chapter Details:
- Chapter: "${chapterOutline.name}"
- Chapter Description: ${chapterOutline.description}
- Needs Quiz: ${chapterOutline.hasQuiz}

Generate comprehensive chapter content with the following requirements:

Content Guidelines:
- Write comprehensive, educational content in Markdown format
- A Content can consist of many sections (heading)
- Include clear headings, subheadings, and structure
- Use examples, analogies, and practical applications
- Make content engaging and easy to understand
- Aim for 800-1500 words of substantial content
- Use bullet points, numbered lists, and formatting for clarity
- Always include a quiz if it needs one


Quiz Guidelines (if Needs Quiz is true):
- Create 10 questions focused specifically on this chapter
- Questions should test key concepts and understanding from the chapter
- Mix question types: 70% multiple choice, 20% true/false, 10% essay
- For multiple choice: provide 4 options with only one correct answer
- For true/false: make statements that are clearly true or false
- For essay: ask questions that require understanding of chapter concepts
- Set time limit between 5-15 minutes
- Set minimum score between 70-80%
- Distribute points based on question difficulty
`;

  return basePrompt;
};

/**
 * Validate generated content
 * @param content Generated content to validate
 * @param shouldHaveQuiz Whether the content should include a quiz
 * @throws AIGenerationError if validation fails
 */
export const validateGeneratedContent = (content: AIGeneratedContent, shouldHaveQuiz: boolean): void => {
  if (
    !content.content ||
    !Array.isArray(content.content) ||
    content.content.length === 0
  ) {
    throw new AIGenerationError(
      'Invalid generated content: missing or invalid content field',
      'VALIDATION_ERROR'
    );
  }


  const contentText = content.content.map(c => c.value).join("\n").trim();
  if (contentText.length < 100) {
    throw new AIGenerationError(
      'Invalid generated content: content is too short',
      'VALIDATION_ERROR'
    );
  }


  if (shouldHaveQuiz) {
    if (!content.quiz) {
      throw new AIGenerationError(
        'Invalid generated content: quiz is required but missing',
        'VALIDATION_ERROR'
      );
    }

    validateGeneratedQuiz(content.quiz);
  } else {
    if (content.quiz !== null && content.quiz !== undefined) {
      throw new AIGenerationError(
        'Invalid generated content: quiz should be null when not required',
        'VALIDATION_ERROR'
      );
    }
  }
};

/**
 * Validate generated quiz
 * @param quiz Generated quiz to validate
 * @throws AIGenerationError if validation fails
 */
export const validateGeneratedQuiz = (quiz: any): void => {
  if (!quiz.name || typeof quiz.name !== 'string') {
    throw new AIGenerationError(
      'Invalid quiz: missing or invalid name',
      'VALIDATION_ERROR'
    );
  }

  if (!quiz.description || typeof quiz.description !== 'string') {
    throw new AIGenerationError(
      'Invalid quiz: missing or invalid description',
      'VALIDATION_ERROR'
    );
  }

  if (typeof quiz.timeLimit !== 'number' || quiz.timeLimit <= 0) {
    throw new AIGenerationError(
      'Invalid quiz: timeLimit must be a positive number',
      'VALIDATION_ERROR'
    );
  }

  if (typeof quiz.minimumScore !== 'number' || quiz.minimumScore < 0 || quiz.minimumScore > 100) {
    throw new AIGenerationError(
      'Invalid quiz: minimumScore must be a number between 0 and 100',
      'VALIDATION_ERROR'
    );
  }

  if (!Array.isArray(quiz.questions) || quiz.questions.length === 0) {
    throw new AIGenerationError(
      'Invalid quiz: questions must be a non-empty array',
      'VALIDATION_ERROR'
    );
  }

  quiz.questions.forEach((question: any, index: number) => {
    if (!['multiple_choice', 'true_false', 'essay'].includes(question.type)) {
      throw new AIGenerationError(
        `Invalid question at index ${index}: type must be multiple_choice, true_false, or essay`,
        'VALIDATION_ERROR'
      );
    }

    if (
      !Array.isArray(question.question) ||
      question.question.length === 0
    ) {
      throw new AIGenerationError(
        `Invalid question at index ${index}: missing or invalid question text`,
        'VALIDATION_ERROR'
      );
    }


    if (question.type === 'multiple_choice') {
      if (!Array.isArray(question.options) || question.options.length < 2) {
        throw new AIGenerationError(
          `Invalid question at index ${index}: multiple choice questions must have at least 2 options`,
          'VALIDATION_ERROR'
        );
      }
    }

    if (question.type === 'essay') {
      if (!question.essayAnswer || typeof question.essayAnswer !== 'string') {
        throw new AIGenerationError(
          `Invalid question at index ${index}: missing or invalid essayAnswer`,
          'VALIDATION_ERROR'
        );
      }
    } else if (question.type === 'multiple_choice') {
      if (
        !Array.isArray(question.options) ||
        question.options.length < 2 ||
        !question.options.some((opt: OptionBlock) => opt.isCorrect === true)
      ) {
        throw new AIGenerationError(
          `Invalid question at index ${index}: must have options with at least one correct answer`,
          'VALIDATION_ERROR'
        );
      }
    }


    if (typeof question.points !== 'number' || question.points <= 0) {
      throw new AIGenerationError(
        `Invalid question at index ${index}: points must be a positive number`,
        'VALIDATION_ERROR'
      );
    }
  });
};

/**
 * Generate content for multiple chapters in sequence
 * @param chapters Array of chapter outlines
 * @param moduleContext Module context
 * @param courseContext Course context
 * @param onProgress Callback for progress updates
 * @returns Promise<AIGeneratedContent[]> Array of generated content
 */
export const generateMultipleChapterContent = async (
  chapters: AIChapterOutline[],
  moduleContext: AIModuleOutline,
  courseContext: AICourseOutline
): Promise<AIGeneratedContent[]> => {
  const results: AIGeneratedContent[] = [];
  
  for (let i = 0; i < chapters.length; i++) {
    const chapter = chapters[i];
    
    try {
      const content = await generateChapterContentLegacy(chapter, moduleContext, courseContext);
      results.push(content);
    } catch (error) {
      throw new AIGenerationError(
        `Failed to generate content for chapter ${i + 1}/${chapters.length}: ${chapter.name}`,
        'BATCH_GENERATION_ERROR',
        error
      );
    }
  }
  
  return results;
};