'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Upload, 
  FileText, 
  Brain, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Edit3, 
  Plus, 
  Minus,
  Loader2,
  AlertCircle,
  BookOpen,
  GraduationCap,
  Target
} from 'lucide-react';
import { toast } from 'sonner';

// Import our modular Gemini functions
import {
  generateCourseOutlineFromPDF,
  generateCourseContentBatch,
  estimateGenerationTime,
  type AICourseOutline,
  type AIGeneratedContent,
  type AIGeneratedQuiz,
  type GenerationProgress,
  type GenerationStep,
  type BatchGenerationResults,
  AIGenerationError
} from '@/lib/gemini';

// Import course creation wizard types
import { CourseData, ModuleData, ChapterData } from '@/components/course/course-creation-wizard';

interface GeneratePageState {
  step: 'upload' | 'outline' | 'generating' | 'complete';
  uploadedFile: File | null;
  courseOutline: AICourseOutline | null;
  editableOutline: AICourseOutline | null;
  generationProgress: GenerationProgress | null;
  generationResults: BatchGenerationResults | null;
  isGenerating: boolean;
  error: string | null;
  sessionId?: string;
}

interface EditableModule {
  name: string;
  description: string;
  chapters: EditableChapter[];
  hasModuleQuiz: boolean;
}

interface EditableChapter {
  name: string;
  description: string;
  hasQuiz: boolean;
}

export default function GenerateCoursePage() {
  const router = useRouter();
  const [state, setState] = useState<GeneratePageState>({
    step: 'upload',
    uploadedFile: null,
    courseOutline: null,
    editableOutline: null,
    generationProgress: null,
    generationResults: null,
    isGenerating: false,
    error: null
  });

  // Handle file upload
  const handleFileUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (file.type !== 'application/pdf') {
      toast.error('Please upload a PDF file');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast.error('File size must be less than 10MB');
      return;
    }

    setState(prev => ({ ...prev, uploadedFile: file, error: null }));
    toast.success('PDF uploaded successfully');
  }, []);

  // Generate course outline from PDF
  const handleGenerateOutline = useCallback(async () => {
    if (!state.uploadedFile) return;

    setState(prev => ({ ...prev, isGenerating: true, error: null }));

    try {
      // Create a unique session ID for this course generation
      const sessionId = `course-generation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const outline = await generateCourseOutlineFromPDF(
        state.uploadedFile, 
        sessionId,
        {
          maxModules: 6,
          maxChaptersPerModule: 8,
          includeQuizzes: true,
          includeFinalExam: true
        }
      );

      // Store the session ID for later use in content generation
      setState(prev => ({
        ...prev,
        courseOutline: outline,
        editableOutline: JSON.parse(JSON.stringify(outline)), // Deep copy
        step: 'outline',
        isGenerating: false,
        sessionId // Add sessionId to state
      }));

      toast.success('Course outline generated successfully!');
    } catch (error) {
      const errorMessage = error instanceof AIGenerationError 
        ? error.message 
        : 'Failed to generate course outline';
      
      setState(prev => ({ 
        ...prev, 
        error: errorMessage, 
        isGenerating: false 
      }));
      
      toast.error(errorMessage);
    }
  }, [state.uploadedFile]);

  // Update editable outline
  const updateOutline = useCallback((updatedOutline: AICourseOutline) => {
    setState(prev => ({ ...prev, editableOutline: updatedOutline }));
  }, []);

  // Start content generation
  const handleStartGeneration = useCallback(async () => {
    if (!state.editableOutline) return;

    setState(prev => ({ 
      ...prev, 
      step: 'generating', 
      isGenerating: true, 
      error: null 
    }));

    try {
      // Use the same session ID from outline generation, or create a new one
      const sessionId = state.sessionId || `course-generation-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      
      const results = await generateCourseContentBatch(
        state.editableOutline,
        (progress) => {
          setState(prev => ({ ...prev, generationProgress: progress }));
        },
        (step, result) => {
          console.log(`Completed step: ${step.name}`, result);
        },
        sessionId
      );

      setState(prev => ({
        ...prev,
        generationResults: results,
        step: 'complete',
        isGenerating: false
      }));

      toast.success('Course content generated successfully!');
    } catch (error) {
      const errorMessage = error instanceof AIGenerationError 
        ? error.message 
        : 'Failed to generate course content';
      
      setState(prev => ({ 
        ...prev, 
        error: errorMessage, 
        isGenerating: false 
      }));
      
      toast.error(errorMessage);
    }
  }, [state.editableOutline]);

  // Convert AI outline to Course Creation Wizard format
  const convertToCourseData = useCallback((): CourseData => {
    if (!state.editableOutline || !state.generationResults) {
      throw new Error('No outline or results available');
    }

    const modules: ModuleData[] = state.editableOutline.modules.map((module, moduleIndex) => {
      const chapters: ChapterData[] = module.chapters.map((chapter, chapterIndex) => {
        const chapterKey = `${moduleIndex}-${chapterIndex}`;
        const generatedContent = state.generationResults?.chapters.get(chapterKey);
        
        return {
          id: `chapter-${moduleIndex}-${chapterIndex}`,
          name: chapter.name,
          description: chapter.description,
          content: generatedContent?.content || [],
          orderIndex: chapterIndex, // Added missing property
          hasChapterQuiz: chapter.hasQuiz, // Added missing property
          chapterQuiz: chapter.hasQuiz && generatedContent?.quiz ? {
            id: `quiz-${moduleIndex}-${chapterIndex}`,
            name: generatedContent.quiz.name,
            description: generatedContent.quiz.description,
            timeLimit: generatedContent.quiz.timeLimit, // Added timeLimit
            minimumScore: generatedContent.quiz.minimumScore, // Added minimumScore
            questions: generatedContent.quiz.questions.map((q, qIndex) => ({
              id: `question-${moduleIndex}-${chapterIndex}-${qIndex}`,
              question: q.question,
              type: q.type as 'multiple_choice' | 'true_false' | 'essay',
              options: (q.type === 'multiple_choice' || q.type === 'true_false')
                ? (q.options?.map((option, optIndex) => ({
                    content: option.content,
                    isCorrect: option.isCorrect
                  })) || [])
                : [],
              essayAnswer: q.type === 'essay' ? q.essayAnswer : null,
              explanation: q.explanation,
              points: q.points || 1,
              orderIndex: qIndex
            }))
          } : undefined
        };
      });

      const moduleQuiz = state.generationResults?.moduleQuizzes.get(moduleIndex);
      
      return {
        id: `module-${moduleIndex}`,
        name: module.name,
        description: module.description,
        orderIndex: moduleIndex, // Added missing property
        chapters,
        hasModuleQuiz: module.hasModuleQuiz, // Added missing property
        moduleQuiz: module.hasModuleQuiz && moduleQuiz ? {
          id: `module-quiz-${moduleIndex}`,
          name: moduleQuiz.name,
          description: moduleQuiz.description,
          timeLimit: moduleQuiz.timeLimit, // Added timeLimit
          minimumScore: moduleQuiz.minimumScore, // Added minimumScore
          questions: moduleQuiz.questions.map((q, qIndex) => ({
            id: `module-question-${moduleIndex}-${qIndex}`,
            question: q.question,
            type: q.type as 'multiple_choice' | 'true_false' | 'essay',
            options: (q.type === 'multiple_choice' || q.type === 'true_false')
              ? (q.options?.map((option, optIndex) => ({
                  content: option.content,
                  isCorrect: option.isCorrect
                })) || [])
              : [],
            essayAnswer: q.type === 'essay' ? q.essayAnswer : null,
            explanation: q.explanation,
            points: q.points || 1,
            orderIndex: qIndex
          }))
        } : undefined
      };
    });

    const finalExam = state.generationResults?.finalExam;

    return {
      name: state.editableOutline.courseName, // Changed title to name and used courseName
      description: state.editableOutline.description,
      instructor: '', // Default value, will be filled in wizard
      courseCode: '', // Default value, as AICourseOutline doesn't have this
      type: 'self_paced', // Default value
      enrollmentType: 'code', // Default value
      startDate: undefined, // Default value
      endDate: undefined, // Default value
      coverImage: undefined, // Default value
      coverImagePreview: undefined, // Default value
      modules,
      isPublished: false, // Default value
      assignedClasses: [], // Default value
      finalExam: finalExam ? {
        id: 'final-exam',
        name: finalExam.name,
        description: finalExam.description,
        timeLimit: finalExam.timeLimit, // Added timeLimit
        minimumScore: finalExam.minimumScore, // Added minimumScore
        questions: finalExam.questions.map((q, qIndex) => ({
          id: `final-question-${qIndex}`,
          question: q.question,
          type: q.type as 'multiple_choice' | 'true_false' | 'essay',
          options: (q.type === 'multiple_choice' || q.type === 'true_false')
            ? (q.options?.map((option, optIndex) => ({
                content: option.content,
                isCorrect: option.isCorrect
              })) || [])
            : [],
          essayAnswer: q.type === 'essay' ? q.essayAnswer : null,
          explanation: q.explanation,
          points: q.points || 1,
          orderIndex: qIndex
        }))
      } : undefined
    };
  }, [state.editableOutline, state.generationResults]);

  // Navigate to course creation wizard with generated data
  const handleProceedToWizard = useCallback(() => {
    try {
      const courseData = convertToCourseData();
      
      console.log('AI Generated Course Data:', courseData); // Log the data
      // Store the generated data in sessionStorage for the wizard
      sessionStorage.setItem('ai_generated_course_data', JSON.stringify(courseData));
      
      // Navigate to the course creation wizard
      router.push('/dashboard/teacher/courses/new?from=generate');
    } catch (error) {
      toast.error('Failed to prepare course data for wizard');
      console.error('Error converting to course data:', error);
    }
  }, [convertToCourseData, router]);

  // Render upload step
  const renderUploadStep = () => (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Upload className="w-6 h-6 text-blue-600" />
          </div>
          <CardTitle>Upload Course Material</CardTitle>
          <CardDescription>
            Upload a PDF document to generate a comprehensive course outline using AI
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors">
            <FileText className="mx-auto w-12 h-12 text-gray-400 mb-4" />
            <Label htmlFor="pdf-upload" className="cursor-pointer">
              <span className="text-lg font-medium">Choose PDF file</span>
              <p className="text-sm text-gray-500 mt-1">Maximum file size: 10MB</p>
            </Label>
            <Input
              id="pdf-upload"
              type="file"
              accept=".pdf"
              onChange={handleFileUpload}
              className="hidden"
            />
          </div>
          
          {state.uploadedFile && (
            <div className="flex items-center gap-3 p-4 bg-green-50 rounded-lg">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-800">{state.uploadedFile.name}</p>
                <p className="text-sm text-green-600">
                  {(state.uploadedFile.size / (1024 * 1024)).toFixed(2)} MB
                </p>
              </div>
            </div>
          )}

          <Button 
            onClick={handleGenerateOutline}
            disabled={!state.uploadedFile || state.isGenerating}
            className="w-full"
            size="lg"
          >
            {state.isGenerating ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Generating Outline...
              </>
            ) : (
              <>
                <Brain className="w-4 h-4 mr-2" />
                Generate Course Outline
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </div>
  );

  // Render outline editing step
  const renderOutlineStep = () => (
    <div className="max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Edit3 className="w-5 h-5" />
            Review & Edit Course Outline
          </CardTitle>
          <CardDescription>
            Review the AI-generated course outline and make any necessary adjustments
          </CardDescription>
        </CardHeader>
        <CardContent>
          {state.editableOutline && (
            <OutlineEditor 
              outline={state.editableOutline}
              onUpdate={updateOutline}
            />
          )}
          
          <div className="flex justify-between mt-8">
            <Button 
              variant="outline" 
              onClick={() => setState(prev => ({ ...prev, step: 'upload' }))}
            >
              Back to Upload
            </Button>
            
            <div className="flex gap-3">
              <div className="text-sm text-gray-500 flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Estimated time: {state.editableOutline ? estimateGenerationTime(state.editableOutline) : 0} minutes
              </div>
              <Button onClick={handleStartGeneration}>
                <Brain className="w-4 h-4 mr-2" />
                Generate Course Content
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  // Render generation progress step
  const renderGeneratingStep = () => (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Brain className="w-6 h-6 text-blue-600 animate-pulse" />
          </div>
          <CardTitle>Generating Course Content</CardTitle>
          <CardDescription>
            AI is creating comprehensive content for your course. This may take several minutes.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {state.generationProgress && (
            <>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{state.generationProgress.completedSteps} / {state.generationProgress.totalSteps}</span>
                </div>
                <Progress 
                  value={(state.generationProgress.completedSteps / state.generationProgress.totalSteps) * 100} 
                  className="h-2"
                />
              </div>
              
              <div className="text-center">
                <p className="font-medium">{state.generationProgress.currentStep}</p>
                <p className="text-sm text-gray-500 mt-1">
                  {state.generationProgress.isGenerating ? 'Generating...' : 'Completed'}
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );

  // Render completion step
  const renderCompleteStep = () => (
    <div className="max-w-2xl mx-auto">
      <Card>
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="w-6 h-6 text-green-600" />
          </div>
          <CardTitle>Course Generated Successfully!</CardTitle>
          <CardDescription>
            Your AI-generated course is ready. You can now proceed to the Course Creation Wizard to finalize and publish it.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {state.generationResults && (
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="p-4 bg-blue-50 rounded-lg">
                <BookOpen className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                <p className="font-medium">{state.generationResults.chapters.size}</p>
                <p className="text-sm text-gray-600">Chapters</p>
              </div>
              <div className="p-4 bg-green-50 rounded-lg">
                <Target className="w-8 h-8 text-green-600 mx-auto mb-2" />
                <p className="font-medium">{state.generationResults.moduleQuizzes.size}</p>
                <p className="text-sm text-gray-600">Module Quizzes</p>
              </div>
              <div className="p-4 bg-purple-50 rounded-lg">
                <GraduationCap className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                <p className="font-medium">{state.generationResults.finalExam ? 1 : 0}</p>
                <p className="text-sm text-gray-600">Final Exam</p>
              </div>
            </div>
          )}
          
          {state.generationResults?.errors && state.generationResults.errors.length > 0 && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Some content generation steps encountered errors. You can review and fix these in the Course Creation Wizard.
              </AlertDescription>
            </Alert>
          )}
          
          <div className="flex gap-3">
            <Button 
              variant="outline" 
              onClick={() => setState(prev => ({ 
                ...prev, 
                step: 'upload',
                uploadedFile: null,
                courseOutline: null,
                editableOutline: null,
                generationResults: null
              }))}
              className="flex-1"
            >
              Generate Another Course
            </Button>
            <Button onClick={handleProceedToWizard} className="flex-1">
              Proceed to Course Wizard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">AI Course Generator</h1>
        <p className="text-gray-600">
          Transform your PDF materials into comprehensive courses with AI-powered content generation
        </p>
      </div>

      {state.error && (
        <Alert className="mb-6 max-w-2xl mx-auto">
          <XCircle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {state.step === 'upload' && renderUploadStep()}
      {state.step === 'outline' && renderOutlineStep()}
      {state.step === 'generating' && renderGeneratingStep()}
      {state.step === 'complete' && renderCompleteStep()}
    </div>
  );
}

// Outline Editor Component
interface OutlineEditorProps {
  outline: AICourseOutline;
  onUpdate: (outline: AICourseOutline) => void;
}

function OutlineEditor({ outline, onUpdate }: OutlineEditorProps) {
  const updateCourseInfo = (field: keyof AICourseOutline, value: any) => {
    onUpdate({ ...outline, [field]: value });
  };

  const updateModule = (moduleIndex: number, field: keyof EditableModule, value: any) => {
    const updatedModules = [...outline.modules];
    updatedModules[moduleIndex] = { ...updatedModules[moduleIndex], [field]: value };
    onUpdate({ ...outline, modules: updatedModules });
  };

  const updateChapter = (moduleIndex: number, chapterIndex: number, field: keyof EditableChapter, value: any) => {
    const updatedModules = [...outline.modules];
    const updatedChapters = [...updatedModules[moduleIndex].chapters];
    updatedChapters[chapterIndex] = { ...updatedChapters[chapterIndex], [field]: value };
    updatedModules[moduleIndex] = { ...updatedModules[moduleIndex], chapters: updatedChapters };
    onUpdate({ ...outline, modules: updatedModules });
  };

  const addModule = () => {
    const newModule = {
      name: 'New Module',
      description: 'Module description',
      chapters: [{
        name: 'New Chapter',
        description: 'Chapter description',
        hasQuiz: false
      }],
      hasModuleQuiz: false
    };
    onUpdate({ ...outline, modules: [...outline.modules, newModule] });
  };

  const removeModule = (moduleIndex: number) => {
    const updatedModules = outline.modules.filter((_, index) => index !== moduleIndex);
    onUpdate({ ...outline, modules: updatedModules });
  };

  const addChapter = (moduleIndex: number) => {
    const newChapter = {
      name: 'New Chapter',
      description: 'Chapter description',
      hasQuiz: false
    };
    const updatedModules = [...outline.modules];
    updatedModules[moduleIndex] = {
      ...updatedModules[moduleIndex],
      chapters: [...updatedModules[moduleIndex].chapters, newChapter]
    };
    onUpdate({ ...outline, modules: updatedModules });
  };

  const removeChapter = (moduleIndex: number, chapterIndex: number) => {
    const updatedModules = [...outline.modules];
    const updatedChapters = updatedModules[moduleIndex].chapters.filter((_, index) => index !== chapterIndex);
    updatedModules[moduleIndex] = { ...updatedModules[moduleIndex], chapters: updatedChapters };
    onUpdate({ ...outline, modules: updatedModules });
  };

  return (
    <div className="space-y-6">
      {/* Course Info */}
      <div className="space-y-4">
        <div>
          <Label htmlFor="course-title">Course Title</Label>
          <Input
            id="course-title"
            value={outline.courseName}
            onChange={(e) => onUpdate({ ...outline, courseName: e.target.value })}
            className="mt-1"
          />
        </div>
        <div>
          <Label htmlFor="course-description">Course Description</Label>
          <Textarea
            id="course-description"
            value={outline.description}
            onChange={(e) => updateCourseInfo('description', e.target.value)}
            className="mt-1"
            rows={3}
          />
        </div>
      </div>

      <Separator />

      {/* Modules */}
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <h3 className="text-lg font-semibold">Course Modules</h3>
          <Button onClick={addModule} size="sm">
            <Plus className="w-4 h-4 mr-1" />
            Add Module
          </Button>
        </div>

        {outline.modules.map((module, moduleIndex) => (
          <Card key={moduleIndex}>
            <CardHeader>
              <div className="flex justify-between items-start">
                <div className="flex-1 space-y-2">
                  <Input
                    value={module.name}
                    onChange={(e) => updateModule(moduleIndex, 'name', e.target.value)}
                    className="font-medium"
                  />
                  <Textarea
                    value={module.description}
                    onChange={(e) => updateModule(moduleIndex, 'description', e.target.value)}
                    rows={2}
                  />
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeModule(moduleIndex)}
                  className="text-red-600 hover:text-red-700"
                >
                  <Minus className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {/* Chapters */}
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <Label className="text-sm font-medium">Chapters</Label>
                    <Button
                      onClick={() => addChapter(moduleIndex)}
                      size="sm"
                      variant="outline"
                    >
                      <Plus className="w-3 h-3 mr-1" />
                      Add Chapter
                    </Button>
                  </div>
                  
                  {module.chapters.map((chapter, chapterIndex) => (
                    <div key={chapterIndex} className="flex gap-2 items-start p-3 border rounded">
                      <div className="flex-1 space-y-2">
                        <Input
                          value={chapter.name}
                          onChange={(e) => updateChapter(moduleIndex, chapterIndex, 'name', e.target.value)}
                          placeholder="Chapter name"
                          className="text-sm"
                        />
                        <Input
                          value={chapter.description}
                          onChange={(e) => updateChapter(moduleIndex, chapterIndex, 'description', e.target.value)}
                          placeholder="Chapter description"
                          className="text-sm"
                        />
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            id={`chapter-quiz-${moduleIndex}-${chapterIndex}`}
                            checked={chapter.hasQuiz}
                            onChange={(e) => updateChapter(moduleIndex, chapterIndex, 'hasQuiz', e.target.checked)}
                          />
                          <Label htmlFor={`chapter-quiz-${moduleIndex}-${chapterIndex}`} className="text-xs">
                            Include chapter quiz
                          </Label>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeChapter(moduleIndex, chapterIndex)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Minus className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>

                {/* Module Quiz Option */}
                <div className="flex items-center gap-2 pt-2 border-t">
                  <input
                    type="checkbox"
                    id={`module-quiz-${moduleIndex}`}
                    checked={module.hasModuleQuiz}
                    onChange={(e) => updateModule(moduleIndex, 'hasModuleQuiz', e.target.checked)}
                  />
                  <Label htmlFor={`module-quiz-${moduleIndex}`} className="text-sm">
                    Include module quiz
                  </Label>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Final Exam Option */}
      <div className="flex items-center gap-2 p-4 border rounded">
        <input
          type="checkbox"
          id="final-exam"
          checked={outline.hasFinalExam}
          onChange={(e) => updateCourseInfo('hasFinalExam', e.target.checked)}
        />
        <Label htmlFor="final-exam">Include final exam</Label>
      </div>
    </div>
  );
}