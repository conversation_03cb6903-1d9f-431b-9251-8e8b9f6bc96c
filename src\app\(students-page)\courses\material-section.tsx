import React, { useState, useCallback } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  ArrowDown01Icon as ChevronDownIcon,
  Book02Icon,
  File01Icon as FileTextIcon,
  PlayIcon,
  Download01Icon,
  ArrowUpRight01Icon as ExternalLinkIcon,
  CheckmarkCircle02Icon,
  BookOpen01Icon as BookOpenIcon,
  UserIcon as UsersIcon
} from 'hugeicons-react';

// Types and Interfaces
interface Content {
  id: string;
  title: string;
  type: 'text' | 'video';
  content: string;
  createdAt: string;
}

interface Chapter {
  id: string;
  title: string;
  order: number;
  contents: Content[];
}

interface Material {
  id: string;
  title: string;
  description: string;
  status: string;
  createdAt: string;
  chapters: Chapter[];
}

// Mock data for testing
const mockMaterials: Material[] = [
  {
    id: '1',
    title: 'Introduction to Biology',
    description: 'Comprehensive guide to fundamental biological concepts',
    status: 'published',
    createdAt: '2024-01-15',
    chapters: [
      {
        id: 'ch1',
        title: 'Chapter 1: Cell Biology',
        order: 1,
        contents: [
          {
            id: 'text1',
            title: 'What are Cells?',
            type: 'text',
            content:
              'Cells are the fundamental units of life. They are the smallest structural and functional units of all living organisms. Every living thing, from the tiniest bacteria to the largest whale, is made up of one or more cells. Cells carry out all the processes necessary for life, including metabolism, growth, reproduction, and response to environmental stimuli.',
            createdAt: '2024-01-15'
          },
          {
            id: 'video1',
            title: 'Introduction to Cell Structure',
            type: 'video',
            content: 'https://example.com/biology/cell-structure-intro',
            createdAt: '2024-01-16'
          },
          {
            id: 'text2',
            title: 'Cell Types',
            type: 'text',
            content:
              'There are two main types of cells: prokaryotic and eukaryotic. Prokaryotic cells, found in bacteria and archaea, lack a membrane-bound nucleus. Eukaryotic cells, found in plants, animals, fungi, and protists, have a nucleus enclosed by a nuclear membrane.',
            createdAt: '2024-01-17'
          }
        ]
      },
      {
        id: 'ch2',
        title: 'Chapter 2: Genetics',
        order: 2,
        contents: [
          {
            id: 'text3',
            title: 'Basics of Inheritance',
            type: 'text',
            content:
              'Genetics is the scientific study of heredity and the variation of inherited characteristics. It involves the study of genes, genetic variation, and heredity in organisms. Genes are made of DNA and act as instructions to make molecules called proteins.',
            createdAt: '2024-01-17'
          },
          {
            id: 'video2',
            title: 'DNA and Genetic Inheritance',
            type: 'video',
            content: 'https://example.com/biology/dna-inheritance',
            createdAt: '2024-01-18'
          },
          {
            id: 'text4',
            title: 'Mendelian Genetics',
            type: 'text',
            content:
              "Gregor Mendel's work with pea plants laid the foundation for our understanding of inheritance. His laws describe how traits are passed from parents to offspring through discrete units called genes.",
            createdAt: '2024-01-19'
          }
        ]
      },
      {
        id: 'ch3',
        title: 'Chapter 3: Ecology',
        order: 3,
        contents: [
          {
            id: 'text5',
            title: 'Ecosystems and Energy Flow',
            type: 'text',
            content:
              'Ecology is the study of interactions between organisms and their environment. Ecosystems are complex networks of interactions between living organisms and their physical environment. Energy flows through ecosystems from producers to consumers.',
            createdAt: '2024-01-20'
          },
          {
            id: 'video3',
            title: 'Food Webs and Energy Pyramids',
            type: 'video',
            content: 'https://example.com/biology/food-webs',
            createdAt: '2024-01-21'
          }
        ]
      }
    ]
  }
];

const ContentItem: React.FC<{
  content: Content;
  isCompleted: boolean;
  onComplete: (id: string) => void;
}> = ({ content, isCompleted, onComplete }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleContentClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setIsExpanded(!isExpanded);
    },
    [isExpanded]
  );

  const handleCompleteClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      onComplete(content.id);
    },
    [content.id, onComplete]
  );

  return (
    <Card className='my-2 ml-6'>
      <CardContent className='py-3'>
        <div className='flex flex-col'>
          <div
            className='flex cursor-pointer items-center justify-between'
            onClick={handleContentClick}
          >
            <div className='flex flex-1 items-center space-x-3'>
              {content.type === 'video' ? (
                <PlayIcon className='h-4 w-4 text-blue-500' />
              ) : (
                <FileTextIcon className='h-4 w-4 text-blue-500' />
              )}
              <span className='text-sm font-medium'>{content.title}</span>
            </div>
            <div className='flex items-center space-x-2'>
              <Button
                size='sm'
                variant={isCompleted ? 'default' : 'outline'}
                className={`min-w-[120px] ${
                  isCompleted
                    ? 'bg-green-600 text-white hover:bg-green-700'
                    : 'text-gray-600 hover:bg-gray-50'
                }`}
                onClick={handleCompleteClick}
              >
                <CheckmarkCircle02Icon className='mr-2 h-4 w-4' />
                {isCompleted ? 'Completed' : 'Mark Complete'}
              </Button>
              <ChevronDownIcon
                className={`h-4 w-4 transform text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
              />
            </div>
          </div>

          {isExpanded && (
            <div className='mt-4 border-t pt-4 pl-7'>
              {content.type === 'text' ? (
                <div className='space-y-4'>
                  <div className='leading-relaxed text-gray-600'>
                    {content.content}
                  </div>
                  <Button
                    size='sm'
                    variant='outline'
                    className='border-blue-200 text-blue-600 hover:bg-blue-50'
                  >
                    <Download01Icon className='mr-2 h-4 w-4' />
                    Download PDF
                  </Button>
                </div>
              ) : (
                <div className='space-y-4'>
                  <div className='flex aspect-video items-center justify-center rounded-lg bg-gray-100'>
                    <PlayIcon className='h-12 w-12 text-gray-400' />
                  </div>
                  <div className='flex space-x-2'>
                    <Button
                      size='sm'
                      variant='outline'
                      className='border-blue-200 text-blue-600 hover:bg-blue-50'
                    >
                      <PlayIcon className='mr-2 h-4 w-4' />
                      Watch Video
                    </Button>
                    <Button
                      size='sm'
                      variant='outline'
                      className='text-gray-600 hover:bg-gray-50'
                    >
                      <ExternalLinkIcon className='mr-2 h-4 w-4' />
                      Open in New Tab
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const ChapterSection: React.FC<{
  chapter: Chapter;
  completedContents: string[];
  onComplete: (id: string) => void;
}> = ({ chapter, completedContents, onComplete }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const completedInChapter = chapter.contents.filter((content) =>
    completedContents.includes(content.id)
  ).length;
  const progress = (completedInChapter / chapter.contents.length) * 100;

  const handleChapterClick = useCallback(
    (e: React.MouseEvent) => {
      e.stopPropagation();
      setIsExpanded(!isExpanded);
    },
    [isExpanded]
  );

  return (
    <Card className='my-2 border border-gray-200'>
      <CardContent className='p-4'>
        <div className='flex flex-col'>
          <div
            className='flex cursor-pointer items-center justify-between'
            onClick={handleChapterClick}
          >
            <div className='flex flex-1 items-center space-x-3'>
              <Book02Icon className='h-5 w-5 text-blue-500' />
              <div className='flex-1'>
                <span className='font-medium'>{chapter.title}</span>
                <div className='mt-2 max-w-md'>
                  <Progress value={progress} className='h-2' />
                </div>
              </div>
              <span className='text-sm text-gray-500'>
                {completedInChapter}/{chapter.contents.length} completed
              </span>
            </div>
            <ChevronDownIcon
              className={`h-5 w-5 transform text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
            />
          </div>

          {isExpanded && (
            <div className='mt-4'>
              {chapter.contents.map((content) => (
                <ContentItem
                  key={content.id}
                  content={content}
                  isCompleted={completedContents.includes(content.id)}
                  onComplete={onComplete}
                />
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

const MaterialSection: React.FC<{
  material: Material;
  isExpanded: boolean;
  onToggle: () => void;
}> = ({ material, isExpanded, onToggle }) => {
  const [completedContents, setCompletedContents] = useState<string[]>([]);

  const totalContents = material.chapters.reduce(
    (sum, chapter) => sum + chapter.contents.length,
    0
  );
  const completedCount = completedContents.length;
  const progress = (completedCount / totalContents) * 100;

  const handleComplete = useCallback((contentId: string) => {
    setCompletedContents((prev) =>
      prev.includes(contentId)
        ? prev.filter((id) => id !== contentId)
        : [...prev, contentId]
    );
  }, []);

  const handleMaterialClick = useCallback(
    (e: React.MouseEvent) => {
      // Only toggle if clicking on the header area, not on child elements
      if (
        e.target === e.currentTarget ||
        (e.target instanceof Node && e.currentTarget.contains(e.target))
      ) {
        onToggle();
      }
    },
    [onToggle]
  );

  return (
    <Card className='bg-white shadow-sm'>
      <CardContent className='p-6'>
        <div className='flex flex-col'>
          <div
            className='flex cursor-pointer items-start justify-between'
            onClick={handleMaterialClick}
          >
            <div className='flex-1 space-y-2'>
              <h3 className='text-lg font-semibold'>{material.title}</h3>
              <p className='text-sm text-gray-600'>{material.description}</p>
              <div className='flex items-center space-x-4'>
                <div className='max-w-md flex-1'>
                  <Progress value={progress} className='h-2' />
                </div>
                <span className='text-sm text-gray-500'>
                  {completedCount}/{totalContents} completed
                </span>
              </div>
            </div>
            <ChevronDownIcon
              className={`h-5 w-5 transform text-gray-400 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}
            />
          </div>

          {isExpanded && (
            <div className='mt-6 border-t pt-4'>
              {material.chapters.map((chapter) => (
                <ChapterSection
                  key={chapter.id}
                  chapter={chapter}
                  completedContents={completedContents}
                  onComplete={handleComplete}
                />
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default function ScrollingTestPage(): React.ReactElement {
  const [selectedMaterial, setSelectedMaterial] = useState<string | null>(null);

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='mx-auto max-w-4xl space-y-6 p-8'>
        {/* Header */}
        <div className='flex items-center space-x-3'>
          <BookOpenIcon className='h-8 w-8 text-blue-600' />
          <div>
            <h1 className='text-2xl font-bold'>
              Course Materials - Scrolling Test
            </h1>
            <p className='text-sm text-gray-600'>
              Test expanding content and scrolling behavior
            </p>
          </div>
        </div>

        {/* Instructions */}
        <Card className='border-blue-200 bg-blue-50'>
          <CardContent className='p-4'>
            <h3 className='mb-2 font-semibold text-blue-900'>
              Test Instructions:
            </h3>
            <ul className='space-y-1 text-sm text-blue-800'>
              <li>
                • Click on &quot;Introduction to Biology&quot; to expand the material
              </li>
              <li>• Click on chapters to expand them</li>
              <li>• Click on individual content items to see full content</li>
              <li>
                • Try scrolling after expanding content - it should work
                normally
              </li>
              <li>• Mark items as complete to test progress tracking</li>
            </ul>
          </CardContent>
        </Card>

        {/* Test Content */}
        <div className='space-y-4'>
          {mockMaterials.map((material) => (
            <MaterialSection
              key={material.id}
              material={material}
              isExpanded={selectedMaterial === material.id}
              onToggle={() =>
                setSelectedMaterial(
                  selectedMaterial === material.id ? null : material.id
                )
              }
            />
          ))}
        </div>

        {/* Bottom spacer */}
        <div className='flex h-20 items-center justify-center text-gray-500'>
          <p>End of content - you should be able to scroll here!</p>
        </div>
      </div>
    </div>
  );
}
