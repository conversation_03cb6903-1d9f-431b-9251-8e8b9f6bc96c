import KBar from '@/components/kbar';
import { EnrollmentProvider } from '@/contexts/enrollment-context';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Course - LMS',
  description: 'Learning Management System - Course View'
};

export default function CourseViewLayout({
  children
}: {
  children: React.ReactNode;
}) {
  return (
    <EnrollmentProvider>
      <KBar>{children}</KBar>
    </EnrollmentProvider>
  );
}
