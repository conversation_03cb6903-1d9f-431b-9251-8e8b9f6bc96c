import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckmarkCircle01Icon as CheckCircle2Icon, Cancel01Icon as XCircleIcon } from 'hugeicons-react';
import { Question as QuestionType } from '@/types/lms';

interface ReviewQuestionProps {
  question: QuestionType;
  questionNumber: number;
  totalQuestions: number;
  userAnswer: number | string | number[] | undefined;
  isCorrect: boolean;
}

export const ReviewQuestion: React.FC<ReviewQuestionProps> = ({
  question,
  questionNumber,
  totalQuestions,
  userAnswer,
  isCorrect
}) => {
  const getQuestionTypeLabel = (type: string) => {
    switch (type) {
      case 'multiple-choice':
        return '<PERSON><PERSON><PERSON>anda';
      case 'true-false':
        return 'Benar/Salah';
      case 'essay':
        return 'Esai';
      default:
        return type;
    }
  };

  const getOptionStyle = (optionIndex: number, isUserAnswer: boolean, isCorrectAnswer: boolean) => {
    if (isCorrectAnswer) {
      return 'border-green-500 bg-green-50 text-green-800';
    }
    if (isUserAnswer && !isCorrectAnswer) {
      return 'border-red-500 bg-red-50 text-red-800';
    }
    return 'border-gray-200 bg-gray-50';
  };

  return (
    <Card className={`
      border-2 transition-all
      ${isCorrect ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}
    `}>
      <CardContent className="p-6">
        <div className="mb-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
              Soal {questionNumber} dari {totalQuestions}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {getQuestionTypeLabel(question.type)}
            </Badge>
          </div>
          <Badge 
            variant={isCorrect ? 'default' : 'destructive'}
            className={`flex items-center space-x-1 ${isCorrect ? 'bg-green-600 hover:bg-green-700' : ''}`}
          >
            {isCorrect ? (
              <CheckCircle2Icon className="h-3 w-3" />
            ) : (
              <XCircleIcon className="h-3 w-3" />
            )}
            <span>{isCorrect ? 'Benar' : 'Salah'}</span>
          </Badge>
        </div>

        <div className="mb-6">
          <p className="text-lg leading-relaxed text-gray-900 whitespace-pre-wrap">
            {typeof question.question === 'string' ? (
              question.question
            ) : Array.isArray(question.question) ? (
              question.question.map((block, index) => (
                <React.Fragment key={index}>
                  {block.type === 'text' && <span>{block.value}</span>}
                  {block.type === 'image' && block.value && (
                    <img src={block.value} alt={`Question image ${index}`} className="inline-block max-h-16 object-contain ml-2" />
                  )}
                  {block.type === 'video' && <span>[Video: {block.value}]</span>}
                  {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}
                  {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}
                </React.Fragment>
              ))
            ) : (
              <span>{String(question.question)}</span>
            )}
          </p>
        </div>

        {/* Multiple Choice Options Review */}
        {question.type === 'multiple-choice' && question.options && (
          <div className="space-y-3">
            {question.options.map((option, index) => {
              const isUserAnswer = userAnswer === index;
              const isCorrectAnswer = question.correctAnswer === index;
              
              return (
                <div
                  key={index}
                  className={`
                    flex items-center space-x-3 p-3 rounded-lg border-2 transition-all
                    ${getOptionStyle(index, isUserAnswer, isCorrectAnswer)}
                  `}
                >
                  <div className="flex items-center space-x-2">
                    <div className={`
                      w-4 h-4 rounded-full border-2 flex items-center justify-center
                      ${isCorrectAnswer ? 'border-green-500 bg-green-500' : 
                        isUserAnswer ? 'border-red-500 bg-red-500' : 'border-gray-400'}
                    `}>
                      {(isUserAnswer || isCorrectAnswer) && (
                        <div className="w-2 h-2 rounded-full bg-white"></div>
                      )}
                    </div>
                    <span className="text-sm font-medium text-gray-600">
                      {String.fromCharCode(65 + index)}.
                    </span>
                  </div>
                  <div className="flex-1 flex items-center justify-between">
                    <span className={`font-medium ${
                      isCorrectAnswer ? 'text-green-800' : 
                      isUserAnswer ? 'text-red-800' : 'text-gray-700'
                    }`}>
                      {typeof option === 'string' ? (
                        option
                      ) : (
                        option.content.map((block, blockIndex) => (
                          <React.Fragment key={blockIndex}>
                            {block.type === 'text' && <span>{block.value}</span>}
                            {block.type === 'image' && block.value && (
                              <img src={block.value} alt={`Option image ${blockIndex}`} className="inline-block max-h-8 object-contain ml-1" />
                            )}
                            {block.type === 'video' && <span>[Video: {block.value}]</span>}
                            {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}
                            {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}
                          </React.Fragment>
                        ))
                      )}
                    </span>
                    {isCorrectAnswer && (
                      <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300 ml-2">
                        Jawaban Benar
                      </Badge>
                    )}
                    {isUserAnswer && !isCorrectAnswer && (
                      <Badge variant="outline" className="bg-red-100 text-red-700 border-red-300 ml-2">
                        Jawaban Anda
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* True/False Review */}
        {question.type === 'true-false' && (
          <div className="space-y-3">
            {['true', 'false'].map((value, index) => {
              const isUserAnswer = userAnswer === value;
              const isCorrectAnswer = question.correctAnswer === value;
              
              return (
                <div
                  key={value}
                  className={`
                    flex items-center space-x-3 p-3 rounded-lg border-2 transition-all
                    ${getOptionStyle(index, isUserAnswer, isCorrectAnswer)}
                  `}
                >
                  <div className="flex items-center space-x-2">
                    <div className={`
                      w-4 h-4 rounded-full border-2 flex items-center justify-center
                      ${isCorrectAnswer ? 'border-green-500 bg-green-500' : 
                        isUserAnswer ? 'border-red-500 bg-red-500' : 'border-gray-400'}
                    `}>
                      {(isUserAnswer || isCorrectAnswer) && (
                        <div className="w-2 h-2 rounded-full bg-white"></div>
                      )}
                    </div>
                    <span className="text-sm font-medium text-gray-600">
                      {String.fromCharCode(65 + index)}.
                    </span>
                  </div>
                  <div className="flex-1 flex items-center justify-between">
                    <span className={`font-medium ${
                      isCorrectAnswer ? 'text-green-800' : 
                      isUserAnswer ? 'text-red-800' : 'text-gray-700'
                    }`}>
                      {value === 'true' ? 'Benar' : 'Salah'}
                    </span>
                    {isCorrectAnswer && (
                      <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300 ml-2">
                        Jawaban Benar
                      </Badge>
                    )}
                    {isUserAnswer && !isCorrectAnswer && (
                      <Badge variant="outline" className="bg-red-100 text-red-700 border-red-300 ml-2">
                        Jawaban Anda
                      </Badge>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}

        {/* Essay Review */}
        {question.type === 'essay' && (
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Jawaban Anda:</h4>
              <div className="p-4 bg-gray-50 rounded-lg border-2 border-gray-200">
                <p className="text-gray-700 whitespace-pre-wrap">
                  {userAnswer || 'Tidak ada jawaban'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Explanation */}
        {question.explanation && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg border-l-4 border-blue-500">
            <h4 className="font-semibold text-blue-900 mb-2 flex items-center space-x-2">
              <span>💡</span>
              <span>Penjelasan:</span>
            </h4>
            <p className="text-sm text-blue-800 leading-relaxed whitespace-pre-wrap">
              {typeof question.explanation === 'string' ? (
                question.explanation
              ) : Array.isArray(question.explanation) ? (
                question.explanation.map((block, index) => (
                  <React.Fragment key={index}>
                    {block.type === 'text' && <span>{block.value}</span>}
                    {block.type === 'image' && block.value && (
                      <img src={block.value} alt={`Explanation image ${index}`} className="inline-block max-h-16 object-contain ml-2" />
                    )}
                    {block.type === 'video' && <span>[Video: {block.value}]</span>}
                    {block.type === 'pdf' && <span>[PDF: {block.value}]</span>}
                    {block.type === 'zoom-recording' && <span>[Recording: {block.value}]</span>}
                  </React.Fragment>
                ))
              ) : (
                <span>{String(question.explanation)}</span>
              )}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};