declare module '@tabler/icons-react' {
  import { FC, SVGProps } from 'react';

  export interface IconProps extends SVGProps<SVGSVGElement> {
    size?: string | number;
    stroke?: string | number;
  }

  export const IconAlertTriangle: FC<IconProps>;
  export const IconArrowRight: FC<IconProps>;
  export const IconCheck: FC<IconProps>;
  export const IconChevronLeft: FC<IconProps>;
  export const IconChevronRight: FC<IconProps>;
  export const IconCommand: FC<IconProps>;
  export const IconCreditCard: FC<IconProps>;
  export const IconFile: FC<IconProps>;
  export const IconFileText: FC<IconProps>;
  export const IconHelpCircle: FC<IconProps>;
  export const IconPhoto: FC<IconProps>;
  export const IconDeviceLaptop: FC<IconProps>;
  export const IconLayoutDashboard: FC<IconProps>;
  export const IconLoader2: FC<IconProps>;
  export const IconLogin: FC<IconProps>;
  export const IconShoppingBag: FC<IconProps>;
  export const IconMoon: FC<IconProps>;
  export const IconDotsVertical: FC<IconProps>;
  export const IconPizza: FC<IconProps>;
  export const IconPlus: FC<IconProps>;
  export const IconSettings: FC<IconProps>;
  export const IconSun: FC<IconProps>;
  export const IconTrash: FC<IconProps>;
  export const IconBrandTwitter: FC<IconProps>;
  export const IconUser: FC<IconProps>;
  export const IconUserCircle: FC<IconProps>;
  export const IconUserEdit: FC<IconProps>;
  export const IconUserX: FC<IconProps>;
  export const IconX: FC<IconProps>;
  export const IconLayoutKanban: FC<IconProps>;
  export const IconBrandGithub: FC<IconProps>;
  export const IconBook: FC<IconProps>;
  export const IconSchool: FC<IconProps>;
  export const IconAward: FC<IconProps>;
  export const IconUserPlus: FC<IconProps>;
  export const IconCertificate: FC<IconProps>;
}
