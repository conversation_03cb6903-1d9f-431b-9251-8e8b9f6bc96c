import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { query } from '@/lib/db/raw';
import { ApiResponse } from '@/types/database';

// GET /api/users - Get all users
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search');
    const role = searchParams.get('role');
    const institutionId = searchParams.get('institutionId');
    const excludeClassId = searchParams.get('excludeClassId');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    let users;
    let countResult;

    if (search && role && role !== 'all' && institutionId && institutionId !== 'all' && institutionId !== 'unassigned') {
      // All filters applied
      const searchPattern = `%${search.toLowerCase()}%`;
      const instId = parseInt(institutionId);
      
      if (excludeClassId) {
        const classId = parseInt(excludeClassId);
        
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
            AND u.institution_id = ${instId}
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
            AND u.institution_id = ${instId}
            AND ce.student_id IS NULL
        `;
      } else {
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
            AND u.institution_id = ${instId}
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
            AND u.institution_id = ${instId}
        `;
      }
    } else if (search && role && role !== 'all') {
      // Search and role filter
      const searchPattern = `%${search.toLowerCase()}%`;
      
      if (excludeClassId) {
        const classId = parseInt(excludeClassId);
        
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
            AND ce.student_id IS NULL
        `;
      } else {
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          WHERE (LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern})
            AND u.role = ${role}
        `;
      }
    } else if (search) {
      // Search filter only
      const searchPattern = `%${search.toLowerCase()}%`;
      
      users = await query`
        SELECT
          u.id,
          u.name,
          u.email,
          u.role,
          u.institution_id,
          u.created_at,
          u.updated_at,
          i.name as institution_name,
          i.type as institution_type
        FROM users u
        LEFT JOIN institutions i ON u.institution_id = i.id
        WHERE LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern}
        ORDER BY u.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      countResult = await query`
        SELECT COUNT(*) as total
        FROM users u
        WHERE LOWER(u.name) LIKE ${searchPattern} OR LOWER(u.email) LIKE ${searchPattern}
      `;
    } else if (role && role !== 'all') {
      // Role filter only
      if (excludeClassId) {
        const classId = parseInt(excludeClassId);
        
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE u.role = ${role}
            AND u.institution_id IS NOT NULL
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE u.role = ${role}
            AND u.institution_id IS NOT NULL
            AND ce.student_id IS NULL
        `;
      } else {
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE u.role = ${role}
            AND u.institution_id IS NOT NULL
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          WHERE u.role = ${role}
            AND u.institution_id IS NOT NULL
        `;
      }
    } else if (institutionId && institutionId === 'unassigned') {
      // Unassigned users only
      users = await query`
        SELECT
          u.id,
          u.name,
          u.email,
          u.role,
          u.institution_id,
          u.created_at,
          u.updated_at,
          i.name as institution_name,
          i.type as institution_type
        FROM users u
        LEFT JOIN institutions i ON u.institution_id = i.id
        WHERE u.institution_id IS NULL
        ORDER BY u.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      countResult = await query`
        SELECT COUNT(*) as total
        FROM users u
        WHERE u.institution_id IS NULL
      `;
    } else if (institutionId && institutionId !== 'all') {
      // Institution filter only
      const instId = parseInt(institutionId);
      
      if (excludeClassId) {
        const classId = parseInt(excludeClassId);
        
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE u.institution_id = ${instId}
            AND ce.student_id IS NULL
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          LEFT JOIN class_enrollments ce ON u.id = ce.student_id AND ce.class_id = ${classId}
          WHERE u.institution_id = ${instId}
            AND ce.student_id IS NULL
        `;
      } else {
        users = await query`
          SELECT
            u.id,
            u.name,
            u.email,
            u.role,
            u.institution_id,
            u.created_at,
            u.updated_at,
            i.name as institution_name,
            i.type as institution_type
          FROM users u
          LEFT JOIN institutions i ON u.institution_id = i.id
          WHERE u.institution_id = ${instId}
          ORDER BY u.created_at DESC
          LIMIT ${limit} OFFSET ${offset}
        `;

        countResult = await query`
          SELECT COUNT(*) as total
          FROM users u
          WHERE u.institution_id = ${instId}
        `;
      }
    } else {
      // No filters
      users = await query`
        SELECT
          u.id,
          u.name,
          u.email,
          u.role,
          u.institution_id,
          u.created_at,
          u.updated_at,
          i.name as institution_name,
          i.type as institution_type
        FROM users u
        LEFT JOIN institutions i ON u.institution_id = i.id
        ORDER BY u.created_at DESC
        LIMIT ${limit} OFFSET ${offset}
      `;

      countResult = await query`
        SELECT COUNT(*) as total
        FROM users u
      `;
    }

    return NextResponse.json({
      success: true,
      data: {
        users,
        total: parseInt(countResult[0].total),
        limit,
        offset
      },
      message: 'Users retrieved successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve users'
      } as ApiResponse,
      { status: 500 }
    );
  }
}

// POST /api/users - Create new user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, email, password, role, institutionId } = body;

    // Validate required fields
    if (!name || !email || !password || !role) {
      return NextResponse.json(
        { success: false, error: 'Name, email, password, and role are required' } as ApiResponse,
        { status: 400 }
      );
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { success: false, error: 'Invalid email format' } as ApiResponse,
        { status: 400 }
      );
    }

    // Check if email already exists
    const existingUser = await query`
      SELECT id FROM users WHERE email = ${email}
    `;

    if (existingUser.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Email already exists' } as ApiResponse,
        { status: 400 }
      );
    }

    // Hash password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // Create user
    const result = await query`
      INSERT INTO users (
        name,
        email,
        password,
        role,
        institution_id
      ) VALUES (
        ${name},
        ${email},
        ${hashedPassword},
        ${role},
        ${institutionId || null}
      ) RETURNING id, name, email, role, institution_id, created_at, updated_at
    `;

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'User created successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Create user error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create user' } as ApiResponse,
      { status: 500 }
    );
  }
}