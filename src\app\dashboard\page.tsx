'use client';

import { useEffect } from 'react';
import { authStorage, getRedirectPath } from '@/lib/auth';

export default function Dashboard() {
  useEffect(() => {
    const user = authStorage.getUser();
    if (user) {
      const redirectPath = getRedirectPath(user);
      window.location.href = redirectPath;
    } else {
      window.location.href = '/auth/sign-in';
    }
  }, []);

  return (
    <div className='flex min-h-screen items-center justify-center'>
      <div className='text-center'>
        <h1 className='text-2xl font-semibold'>Redirecting...</h1>
        <p className='text-muted-foreground'>
          Please wait while we redirect you to your dashboard.
        </p>
      </div>
    </div>
  );
}
