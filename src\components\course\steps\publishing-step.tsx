'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  BookOpen, 
  Users, 
  HelpCircle,
  Calendar,
  Code,
  Image,
  FileText,
  Clock,
  Target,
  Rocket,
  Eye,
  Share2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { CourseData } from '../course-creation-wizard';
import { toast } from 'sonner';

interface PublishingStepProps {
  data: CourseData;
  onPublish: () => Promise<void>;
  isPublishing: boolean;
}

interface ValidationItem {
  id: string;
  label: string;
  status: 'complete' | 'incomplete' | 'warning';
  description: string;
  required: boolean;
}

export function PublishingStep({ data, onPublish, isPublishing }: PublishingStepProps) {
  const [showDetails, setShowDetails] = useState(false);

  const getValidationItems = (): ValidationItem[] => {
    const items: ValidationItem[] = [];

    // Basic course info validation
    items.push({
      id: 'course-name',
      label: 'Nama Course',
      status: data.name.trim() ? 'complete' : 'incomplete',
      description: data.name.trim() ? `"${data.name}"` : 'Nama course harus diisi',
      required: true
    });

    items.push({
      id: 'course-description',
      label: 'Deskripsi Course',
      status: data.description.trim() ? 'complete' : 'incomplete',
      description: data.description.trim() 
        ? `${data.description.length} karakter` 
        : 'Deskripsi course harus diisi',
      required: true
    });

    items.push({
      id: 'course-code',
      label: 'Kode Course',
      status: data.courseCode.trim() ? 'complete' : 'incomplete',
      description: data.courseCode.trim() ? data.courseCode : 'Kode course harus diisi',
      required: true
    });

    items.push({
      id: 'cover-image',
      label: 'Cover Image',
      status: data.coverImage ? 'complete' : 'warning',
      description: data.coverImage ? 'Cover image telah diupload' : 'Disarankan menambahkan cover image',
      required: false
    });

    items.push({
      id: 'course-dates',
      label: 'Tanggal Course',
      status: data.startDate && data.endDate ? 'complete' : 'warning',
      description: data.startDate && data.endDate 
        ? `${new Date(data.startDate).toLocaleDateString()} - ${new Date(data.endDate).toLocaleDateString()}`
        : 'Tanggal mulai dan selesai belum diatur',
      required: false
    });

    // Module structure validation
    const moduleCount = data.modules.length;
    items.push({
      id: 'modules',
      label: 'Struktur Modul',
      status: moduleCount > 0 ? 'complete' : 'incomplete',
      description: moduleCount > 0 
        ? `${moduleCount} modul telah dibuat` 
        : 'Minimal 1 modul harus dibuat',
      required: true
    });

    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);
    items.push({
      id: 'chapters',
      label: 'Chapter',
      status: totalChapters > 0 ? 'complete' : 'incomplete',
      description: totalChapters > 0 
        ? `${totalChapters} chapter telah dibuat` 
        : 'Minimal 1 chapter harus dibuat',
      required: true
    });

    // Content validation
    const chaptersWithContent = data.modules.reduce((acc, module) => 
      acc + module.chapters.filter(chapter => chapter.content && chapter.content.length > 0).length, 0
    );
    
    items.push({
      id: 'content',
      label: 'Konten Chapter',
      status: chaptersWithContent === totalChapters ? 'complete' : 
               chaptersWithContent > 0 ? 'warning' : 'incomplete',
      description: `${chaptersWithContent} dari ${totalChapters} chapter memiliki konten`,
      required: true
    });

    // Quiz validation
    const chaptersWithQuiz = data.modules.reduce((acc, module) => 
      acc + module.chapters.filter(chapter => chapter.hasChapterQuiz && chapter.chapterQuiz).length, 0
    );
    
    const modulesWithQuiz = data.modules.filter(module => module.hasModuleQuiz && module.moduleQuiz).length;
    
    items.push({
      id: 'quizzes',
      label: 'Quiz',
      status: (chaptersWithQuiz > 0 || modulesWithQuiz > 0) ? 'complete' : 'warning',
      description: `${chaptersWithQuiz} chapter quiz, ${modulesWithQuiz} module quiz`,
      required: false
    });

    // Final exam validation
    items.push({
      id: 'final-exam',
      label: 'Final Exam',
      status: data.finalExam ? 'complete' : 'warning',
      description: data.finalExam 
        ? `${data.finalExam.questions.length} pertanyaan` 
        : 'Final exam belum dibuat',
      required: false
    });

    return items;
  };

  const validationItems = getValidationItems();
  const requiredItems = validationItems.filter(item => item.required);
  const completedRequired = requiredItems.filter(item => item.status === 'complete').length;
  const canPublish = completedRequired === requiredItems.length;
  
  const allCompleted = validationItems.filter(item => item.status === 'complete').length;
  const completionPercentage = Math.round((allCompleted / validationItems.length) * 100);

  const getCourseStats = () => {
    const totalChapters = data.modules.reduce((acc, module) => acc + module.chapters.length, 0);
    const totalQuizzes = data.modules.reduce((acc, module) => {
      const chapterQuizzes = module.chapters.filter(c => c.hasChapterQuiz).length;
      const moduleQuiz = module.hasModuleQuiz ? 1 : 0;
      return acc + chapterQuizzes + moduleQuiz;
    }, 0) + (data.finalExam ? 1 : 0);
    
    const estimatedDuration = data.modules.reduce((acc, module) => 
      acc + module.chapters.reduce((chapterAcc, chapter) =>
        chapterAcc + Math.ceil((chapter.content as any[]).filter(block => block.type === 'text').reduce((textAcc, block) => textAcc + block.value.length, 0) / 1000) * 5, 0
      ), 0
    );

    return {
      modules: data.modules.length,
      chapters: totalChapters,
      quizzes: totalQuizzes,
      estimatedDuration: Math.max(estimatedDuration, 30) // minimum 30 minutes
    };
  };

  const stats = getCourseStats();

  const handlePublish = async () => {
    if (!canPublish) {
      toast.error('Lengkapi semua item yang wajib diisi terlebih dahulu');
      return;
    }

    try {
      await onPublish();
      toast.success('Course berhasil dipublikasi!');
    } catch (error) {
      toast.error('Gagal mempublikasi course');
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <div className={cn(
          "w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4",
          canPublish ? "bg-green-100 text-green-600" : "bg-orange-100 text-orange-600"
        )}>
          {canPublish ? (
            <Rocket className="w-8 h-8" />
          ) : (
            <AlertCircle className="w-8 h-8" />
          )}
        </div>
        <h3 className="text-2xl font-bold">
          {canPublish ? 'Siap untuk Dipublikasi!' : 'Hampir Selesai'}
        </h3>
        <p className="text-muted-foreground">
          {canPublish 
            ? 'Course Anda sudah siap untuk dipublikasi dan dapat diakses oleh siswa'
            : 'Lengkapi beberapa item berikut untuk mempublikasi course'
          }
        </p>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <Target className="w-5 h-5" />
                <span>Progress Kelengkapan</span>
              </CardTitle>
              <CardDescription>
                {allCompleted} dari {validationItems.length} item selesai
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">{completionPercentage}%</div>
              <div className="text-sm text-muted-foreground">Selesai</div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Progress value={completionPercentage} className="mb-4" />
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center w-10 h-10 bg-blue-100 text-blue-600 rounded-full mx-auto mb-2">
                <BookOpen className="w-5 h-5" />
              </div>
              <div className="text-sm font-medium">{stats.modules}</div>
              <div className="text-xs text-muted-foreground">Modul</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center w-10 h-10 bg-green-100 text-green-600 rounded-full mx-auto mb-2">
                <FileText className="w-5 h-5" />
              </div>
              <div className="text-sm font-medium">{stats.chapters}</div>
              <div className="text-xs text-muted-foreground">Chapter</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center w-10 h-10 bg-purple-100 text-purple-600 rounded-full mx-auto mb-2">
                <HelpCircle className="w-5 h-5" />
              </div>
              <div className="text-sm font-medium">{stats.quizzes}</div>
              <div className="text-xs text-muted-foreground">Quiz</div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center w-10 h-10 bg-orange-100 text-orange-600 rounded-full mx-auto mb-2">
                <Clock className="w-5 h-5" />
              </div>
              <div className="text-sm font-medium">{stats.estimatedDuration}</div>
              <div className="text-xs text-muted-foreground">Menit</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Validation Checklist */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5" />
              <span>Checklist Publikasi</span>
            </CardTitle>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              {showDetails ? 'Sembunyikan' : 'Lihat'} Detail
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {validationItems.map((item) => (
              <div key={item.id} className="flex items-start space-x-3">
                <div className={cn(
                  "w-5 h-5 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5",
                  item.status === 'complete' ? "bg-green-100 text-green-600" :
                  item.status === 'warning' ? "bg-orange-100 text-orange-600" :
                  "bg-gray-100 text-gray-400"
                )}>
                  {item.status === 'complete' ? (
                    <CheckCircle className="w-3 h-3" />
                  ) : item.status === 'warning' ? (
                    <AlertCircle className="w-3 h-3" />
                  ) : (
                    <div className="w-2 h-2 bg-current rounded-full" />
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className={cn(
                      "text-sm font-medium",
                      item.status === 'complete' ? "text-green-700" :
                      item.status === 'warning' ? "text-orange-700" :
                      "text-gray-500"
                    )}>
                      {item.label}
                    </span>
                    {item.required && (
                      <Badge variant="destructive" className="text-xs px-1 py-0">
                        Wajib
                      </Badge>
                    )}
                  </div>
                  
                  {showDetails && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {item.description}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Course Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Eye className="w-5 h-5" />
            <span>Preview Course</span>
          </CardTitle>
          <CardDescription>
            Begini tampilan course Anda untuk siswa
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="border rounded-lg p-4 space-y-4">
            {/* Course Header */}
            <div className="flex items-start space-x-4">
              {data.coverImage ? (
                <img 
                  src={typeof data.coverImage === 'string' 
                  ? data.coverImage 
                  : URL.createObjectURL(data.coverImage)}
                  alt={data.name}
                  className="w-20 h-20 object-cover rounded-lg"
                />
              ) : (
                <div className="w-20 h-20 bg-muted rounded-lg flex items-center justify-center">
                  <Image className="w-8 h-8 text-muted-foreground" />
                </div>
              )}
              
              <div className="flex-1">
                <h4 className="font-semibold text-lg">{data.name || 'Nama Course'}</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  {data.description || 'Deskripsi course'}
                </p>
                
                <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                  <div className="flex items-center space-x-1">
                    <Code className="w-3 h-3" />
                    <span>{data.courseCode || 'COURSE-CODE'}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <BookOpen className="w-3 h-3" />
                    <span>{stats.modules} Modul</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="w-3 h-3" />
                    <span>~{stats.estimatedDuration} Menit</span>
                  </div>
                  {data.startDate && (
                    <div className="flex items-center space-x-1">
                      <Calendar className="w-3 h-3" />
                      <span>{new Date(data.startDate).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
            
            <Separator />
            
            {/* Module Structure Preview */}
            <div className="space-y-2">
              <h5 className="font-medium text-sm">Struktur Course:</h5>
              {data.modules.length > 0 ? (
                <div className="space-y-2">
                  {data.modules.slice(0, 3).map((module, index) => (
                    <div key={module.id} className="text-sm">
                      <div className="font-medium">
                        {index + 1}. {module.name}
                      </div>
                      <div className="ml-4 text-xs text-muted-foreground">
                        {module.chapters.length} chapter
                        {module.hasModuleQuiz && ' • Quiz modul'}
                      </div>
                    </div>
                  ))}
                  {data.modules.length > 3 && (
                    <div className="text-xs text-muted-foreground">
                      ... dan {data.modules.length - 3} modul lainnya
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-muted-foreground italic">
                  Belum ada modul
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Warnings */}
      {!canPublish && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>Perhatian:</strong> Beberapa item wajib belum lengkap. 
            Course tidak dapat dipublikasi sampai semua item wajib diselesaikan.
          </AlertDescription>
        </Alert>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-between pt-6">
        <div className="text-sm text-muted-foreground">
          {canPublish 
            ? 'Course siap dipublikasi dan dapat diakses siswa'
            : `${completedRequired}/${requiredItems.length} item wajib selesai`
          }
        </div>
        
        <div className="flex items-center space-x-3">
          <Button variant="outline" disabled={isPublishing}>
            <Eye className="w-4 h-4 mr-2" />
            Preview
          </Button>
          
          <Button 
            onClick={handlePublish}
            disabled={!canPublish || isPublishing}
            className="min-w-[120px]"
          >
            {isPublishing ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                Publishing...
              </>
            ) : (
              <>
                <Rocket className="w-4 h-4 mr-2" />
                Publikasi Course
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}