# Overview

We are now working on a Learning Management System (LMS) with the following features:

## Rules
- Use NEON DB for the database
- For Login, just use NEON DB (basic email,password,role is enough), we dont have to implement token, refresh token, etc. Just use basic POST and GET request. and store the user data in localStorage. 
- For the AI, we are going to use Gemini 2.5 Flash. Use the API key in .env.local
- Make all the code as modular as possible, so that it can be easily integrated into other projects. Dont use any external dependencies that are not necessary. And the most important, dont make a too long code. Make it as neat as possible.
- Mainly use Shadcn-ui for the UI components, but if there is already an existing component that can be used, then use it.
- I have made the Sign In and Sign Up Page in 'app/auth'
- The current Dashboard Page is just a template, you can change it to suits our needs.
- Kindly use the existing navbar and sidebar as the main navigation. You can add more pages to it and change it to suits our needs.
- DONT USE Drizzle for API, use query to Neon DB just like the one i already made (to avoid complex methods/objects)


## Goals
**Subscription**
- Name
- Price per Students
    - Monthly : number
    - Yearly : number
- Features : array of string
- minStudents : number
- maxStudents : number
- isPopular : boolean
- idealFor : string
- icon : React.ElementType
- description : string

Example :
  basic: {
    name: "Basic",
    pricePerStudent: {
      monthly: 5000,
      yearly: 45000, // 15000 * 12 * 0.75 (25% discount)
    },
    features: [
      "Basic AI-powered learning",
      "Standard analytics",
      "Email support",
      "Basic question bank",
      "5 teachers per 100 students",
      "Standard reporting",
      "Basic classroom management"
    ],
    minStudents: 50,
    maxStudents: 200,
    isPopular: false,
    idealFor: "Small Schools",
    icon: Shield,
    description: "Perfect for small schools starting their digital transformation journey"
  },
  pro: {
    name: "Professional",
    pricePerStudent: {
      monthly: 8000,
      yearly: 72000, // 25000 * 12 * 0.75
    },
    features: [
      "Advanced AI learning paths",
      "Comprehensive analytics",
      "Priority support",
      "Extended question bank",
      "Custom branding",
      "15 teachers per 100 students",
      "Advanced reporting",
      "Parent portal access",
      "Integration with LMS"
    ],
    minStudents: 100,
    maxStudents: 1000,
    isPopular: true,
    idealFor: "Growing Schools",
    icon: Crown,
    description: "Ideal for growing institutions seeking advanced features and scalability"
  },
  enterprise: {
    name: "Enterprise",
    pricePerStudent: {
      monthly: 12000,
      yearly: 108000, // 35000 * 12 * 0.75
    },
    features: [
      "Full AI capabilities",
      "Enterprise analytics",
      "24/7 dedicated support",
      "Unlimited question bank",
      "API access",
      "Custom integrations",
      "Unlimited teachers",
      "Advanced security features",
      "Custom development options",
      "Dedicated account manager",
      "Multi-campus support"
    ],
    minStudents: 500,
    maxStudents: 5000,
    isPopular: false,
    idealFor: "Large Institutions",
    icon: Sparkles,
    description: "Enterprise-grade solution for large educational institutions"
  }


**Users**
- Name
- Email
- Password
- Role :
    - Student/Participant
    - Institution Manager (Super Admin)
    - Teacher (Admin)
- When first created (Teacher and Student). The insistutionId is null. The user can only access the dashboard. After the institution is created and assigned to the user by the super admin/institution manager. 

**Super Admin Page:**
- **Institution Management**
    - CRUD Institution
    - Institutions have a list of classes and users that are assigned to the institution. the users can also be assigned to a role (Admin/Teacher, Student). They can also assign a user to institution
    - Institutions have a type : "sd-negeri", "sd-swasta", "smp-negeri", "smp-swasta", "sma-negeri", "sma-swasta", "university-negeri", "university-swasta", "institution-training", "institution-course", "institution-other".
    - Institutions have a subscription plan (Basic, Pro, Enterprise) that can be assigned to the institution.
    - Institutions have a billing cycle (Monthly, Yearly) that can be assigned to the institution.
    - Institutions have a payment status (Paid, Unpaid) that can be assigned to the institution.
    - Institutions have a payment due date that can be assigned to the institution.
    - Institutions have a number of students and teachers that can be edited (this numbers dont have to relate to the number of students and teachers that are assigned to the institution)
    - All the other datas (such as course completion, student progress, etc.) are used to generate the reports and analytics for the institution as a dashboard for the institution manager to see.



**Teacher Page:**
- **Class Management**
    - CRUD Class where each class is filled with a list of students and courses that can be assigned to students inside of class
    - Assign Students from the same institution to Class
    - Create Course Code (manual or auto generate)
- **Course Students Enrollment**
    - Enrollment By Code
- **Course Management**
    - CRUD Course
    - The chapter contents are filled with markdown content that can be edited manually, and the navigation of the chapter is automatically assigned using headers of the markdown content (also divided by the levels of headers)
    - Assign Course to Class
    - **AI Autogeneration Material/Module Feature (OUR Main Feature):** Automatically generate a whole course only by uploading a PDF book where the admin can still update and edit it manually (We are gonna use Gemini 2.5 Flash). Before it got generated, we need to ask the AI First of how many modules and chapters it can generate with the names of the modules and chapters. So that the admin can see and edit the outline of the course before it got generated.
        - **Course Name: e.g. Introduction to Architecture (also has start and end timeline, and also divided by 2 categories, one where if the course is finished by the participants, the results and the certificate will automatically be generated (This is called a Self-paced Course), and the other one where the admin have to manually verify the results first before generating the certificate and results (This is called a Verified Course))**
            - Modul 1 (Has a list of Chapters,Quiz, and also start and end timeline for this module where by default its going to follow the course timeline) 
                - Chapter 1 (Has a markdown content and Quiz)
                    - Chapter Quiz (Has a list of Questions where the questions are divided into multiple choice, true or false, and essay)
                    - Config Minimum Score to Pass
                    - This Quiz only tests the understanding of the student for the whole chapter, not the whole module
                - Chapter 2
                    - Chapter Quiz
                        - Config Minimum Score
                - Chapter 3
                    - Chapter Quiz
                        - Config Minimum Score
                - Modul Quiz (Has a list of Questions where the questions are divided into multiple choice, true or false, and essay)
                    - Config Minimum Score to Pass
                    - This Quiz tests the understanding of the student for the whole module
            - Modul 2….
            - Final Exam (Has a list of Questions where the questions are divided into multiple choice, true or false, and essay, and also start and end timeline for this exam where by default its going to follow the course timeline)
                - Config Minimum Score to Pass
                - This Exam tests the understanding of the student for the whole course
            - All the scores are automatically calculated and recorded for each student for each quiz and exam that they have taken based on the correct answers. but it can also be manually edited by the admin if needed (inside reports and analytics).
            - The certificate is automatically generated for each participant for each course that they have taken if the course type is not "Self-paced" (This is called a Verified Course) and the results are validated by the admin (This is called a Self-paced Course).
            - The certificate is a mock for now, we will implement a true certificate later using PDF Lib.
- **Reports and Analytics**
    - To manage, modify, edit, and review/validate the results of each participant's course progress (scores of module by module, chapter by chapter, quiz by quiz, and exam by exam). basically all the datas that are needed to generate the reports and analytics for the institution as a dashboard for the institution manager to see.
    - This is also used to validate for generating the certificate for each participant for each course that they have taken if the course type is not "Self-paced"

- We dont have to implement a true payment system and student page for now. Just focus on the features that i typed above