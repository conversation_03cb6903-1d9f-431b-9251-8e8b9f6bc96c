import { put } from '@vercel/blob';
import { NextResponse } from 'next/server';
 
export async function POST(request: Request): Promise<NextResponse> {
  const { searchParams } = new URL(request.url);
  const filename = searchParams.get('filename');
  console.log('Received upload request for filename:', filename);
 
  if (!filename) {
    console.log('Error: Filename not provided');
    return NextResponse.json({ error: 'Filename not provided' }, { status: 400 });
  }

  // Ensure request.body is not null
  if (!request.body) {
    console.log('Error: Request body is empty');
    return NextResponse.json({ error: 'Request body is empty' }, { status: 400 });
  }

  try {
    const blob = await put(filename, request.body, {
      access: 'public',
    });
   
    return NextResponse.json(blob);
  } catch (error) {
    console.error('Error uploading blob:', error);
    return NextResponse.json({ error: 'Failed to upload file' }, { status: 500 });
  }
}