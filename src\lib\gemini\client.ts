import { GoogleGenAI, Type } from '@google/genai';
import { AIGenerationError } from './types';

// Export Type for external use
export { Type };

// Schema configuration interface for structured output
export interface SchemaConfig {
  responseMimeType: 'application/json';
  responseSchema: any;
}

// Chat history interface
export interface ChatMessage {
  role: 'user' | 'model';
  parts: Array<{ text?: string; inlineData?: { mimeType: string; data: string } }>;
}

// Chat session interface
export interface ChatSession {
  id: string;
  chat: any;
  history: ChatMessage[];
  createdAt: Date;
}

// Singleton instance for Gemini AI client
let geminiInstance: GoogleGenAI | null = null;

// Chat sessions storage
const chatSessions = new Map<string, ChatSession>();

/**
 * Get or create Gemini AI client instance
 * @returns GoogleGenAI instance
 * @throws AIGenerationError if API key is not configured
 */
export const getGeminiClient = (): GoogleGenAI => {
  if (geminiInstance) {
    return geminiInstance;
  }

  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  if (!apiKey) {
    throw new AIGenerationError(
      'Gemini API key is not configured. Please set NEXT_PUBLIC_GEMINI_API_KEY environment variable.',
      'MISSING_API_KEY'
    );
  }

  try {
    geminiInstance = new GoogleGenAI({ apiKey });
    return geminiInstance;
  } catch (error) {
    throw new AIGenerationError(
      'Failed to initialize Gemini AI client',
      'CLIENT_INIT_ERROR',
      error
    );
  }
};

/**
 * Convert file to base64 for Gemini API
 * @param file File to convert
 * @returns Promise<string> Base64 encoded file content
 */
export const fileToBase64 = async (file: File): Promise<string> => {
  try {
    const arrayBuffer = await file.arrayBuffer();
    return Buffer.from(arrayBuffer).toString('base64');
  } catch (error) {
    throw new AIGenerationError(
      'Failed to convert file to base64',
      'FILE_CONVERSION_ERROR',
      error
    );
  }
};

/**
 * Generate content with Gemini API using structured output
 * @param contents Content array for Gemini API
 * @param schema Schema configuration for structured output
 * @param model Model to use (default: gemini-2.5-flash)
 * @returns Promise<T> Parsed JSON object
 */
export const generateWithGeminiStructured = async <T>(
  contents: any[],
  schema: SchemaConfig,
  model: string = 'gemini-2.5-flash'
): Promise<T> => {
  try {
    const client = getGeminiClient();
    const response = await client.models.generateContent({
      model,
      contents,
      config: {
        ...schema,
        thinkingConfig: {
          thinkingBudget: 0 // Disable thinking for faster responses
        }
      }
    });
    const text = response.text || '';
    if (!text.trim()) {
      throw new Error('Empty response from Gemini API');
    }
    
    // With structured output, the response should already be valid JSON
    try {
      const parsed = JSON.parse(text) as T;
      // 🔍 Log hasil JSON setelah parsing
      console.log('[Gemini Structured Output - Parsed]', parsed);
      return parsed;
    } catch (parseError) {
      throw new Error(`Failed to parse structured output: ${parseError}`);
    }
  } catch (error) {
    throw new AIGenerationError(
      'Failed to generate structured content with Gemini API',
      'STRUCTURED_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Create a new chat session with Gemini
 * @param sessionId Unique identifier for the chat session
 * @param initialHistory Optional initial chat history
 * @param model Model to use (default: gemini-2.5-flash)
 * @returns ChatSession object
 */
export const createChatSession = (sessionId: string, initialHistory: ChatMessage[] = [], model: string = 'gemini-2.5-flash'): ChatSession => {
  try {
    const client = getGeminiClient();
    
    const chat = client.chats.create({
      model,
      history: initialHistory
    });

    const session: ChatSession = {
      id: sessionId,
      chat,
      history: [...initialHistory],
      createdAt: new Date()
    };

    chatSessions.set(sessionId, session);
    return session;
  } catch (error) {
    throw new AIGenerationError(
      'Failed to create chat session',
      'CHAT_SESSION_ERROR',
      error
    );
  }
};

/**
 * Get existing chat session or create a new one
 * @param sessionId Unique identifier for the chat session
 * @param initialHistory Optional initial chat history (only used if creating new session)
 * @param model Model to use (default: gemini-2.5-flash)
 * @returns ChatSession object
 */
export const getChatSession = (sessionId: string, initialHistory: ChatMessage[] = [], model: string = 'gemini-2.5-flash'): ChatSession => {
  const existingSession = chatSessions.get(sessionId);
  if (existingSession) {
    return existingSession;
  }
  return createChatSession(sessionId, initialHistory, model);
};

/**
 * Send message to chat session
 * @param sessionId Chat session ID
 * @param message Message to send
 * @returns Promise<string> Response text
 */
export const sendChatMessage = async (sessionId: string, message: ChatMessage): Promise<string> => {
  try {
    const session = chatSessions.get(sessionId);
    if (!session) {
      throw new AIGenerationError(
        `Chat session not found: ${sessionId}`,
        'SESSION_NOT_FOUND'
      );
    }

    // Add user message to history
    session.history.push(message);

    // Send message to chat
    const response = await session.chat.sendMessage({
      message: message.parts.map(part => part.text || '').join(' ')
    });

    const responseText = response.text || '';
    
    // Add model response to history
    session.history.push({
      role: 'model',
      parts: [{ text: responseText }]
    });

    return responseText;
  } catch (error) {
    throw new AIGenerationError(
      'Failed to send chat message',
      'CHAT_MESSAGE_ERROR',
      error
    );
  }
};

/**
 * Send structured message to chat session
 * @param sessionId Chat session ID
 * @param message Message to send
 * @param schema Schema configuration for structured output
 * @returns Promise<T> Parsed JSON response
 */
export const sendStructuredChatMessage = async <T>(
  sessionId: string,
  message: ChatMessage,
  schema: SchemaConfig
): Promise<T> => {
  try {
    const session = chatSessions.get(sessionId);
    if (!session) {
      throw new AIGenerationError(
        `Chat session not found: ${sessionId}`,
        'SESSION_NOT_FOUND'
      );
    }

    // Add user message to history
    session.history.push(message);

    // Create a new chat with updated history and schema config
    const client = getGeminiClient();
    console.log(`[${sessionId}] Sending history (structured):`);
    console.table(session.history); // opsional: lebih rapi untuk array
    console.groupEnd();
    const chatWithSchema = client.chats.create({
      model: 'gemini-2.5-flash',
      history: session.history,
      config: {
        ...schema,
        thinkingConfig: {
          thinkingBudget: 0 // Disable thinking for faster responses
        }
      }
    });

    // Send message
    const response = await chatWithSchema.sendMessage({
      message: message.parts.map(part => part.text || '').join(' ')
    });

    const responseText = response.text || '';
    
    if (!responseText.trim()) {
      throw new Error('Empty response from Gemini API');
    }

    // Parse JSON response
    let parsed: T;
    try {
      parsed = JSON.parse(responseText) as T;
      console.log('[Gemini Chat Structured Output - Parsed]', parsed);
    } catch (parseError) {
      throw new Error(`Failed to parse structured output: ${parseError}`);
    }

    // Add model response to history
    session.history.push({
      role: 'model',
      parts: [{ text: responseText }]
    });

    return parsed;
  } catch (error) {
    throw new AIGenerationError(
      'Failed to send structured chat message',
      'STRUCTURED_CHAT_ERROR',
      error
    );
  }
};

/**
 * Clear chat session
 * @param sessionId Chat session ID
 */
export const clearChatSession = (sessionId: string): void => {
  chatSessions.delete(sessionId);
};

/**
 * Get chat session history
 * @param sessionId Chat session ID
 * @returns ChatMessage[] Chat history
 */
export const getChatHistory = (sessionId: string): ChatMessage[] => {
  const session = chatSessions.get(sessionId);
  return session ? [...session.history] : [];
};

/**
 * Reset the Gemini client instance (useful for testing)
 */
export const resetGeminiClient = (): void => {
  geminiInstance = null;
  chatSessions.clear();
};