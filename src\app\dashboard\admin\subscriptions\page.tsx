'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { toast } from 'sonner';
import { Search, MoreHorizontal, CalendarIcon, DollarSign, Users, Building, AlertTriangle, CheckCircle, XCircle } from 'lucide-react';
import { format } from 'date-fns';

interface Institution {
  id: number;
  name: string;
  type: string;
  subscription_plan: string;
  billing_cycle: string;
  payment_status: 'paid' | 'unpaid';
  payment_due_date: string | null;
  student_count: number;
  teacher_count: number;
  actual_student_count: number;
  actual_teacher_count: number;
  created_at: string;
  updated_at: string;
}

interface BillingSummary {
  totalInstitutions: number;
  paidInstitutions: number;
  unpaidInstitutions: number;
  overdueInstitutions: number;
  totalStudents: number;
  totalTeachers: number;
}

interface ApiResponse {
  success: boolean;
  data?: {
    institutions: Institution[];
    total: number;
    summary: BillingSummary;
  };
  error?: string;
}

export default function SubscriptionsPage() {
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [summary, setSummary] = useState<BillingSummary>({
    totalInstitutions: 0,
    paidInstitutions: 0,
    unpaidInstitutions: 0,
    overdueInstitutions: 0,
    totalStudents: 0,
    totalTeachers: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [planFilter, setPlanFilter] = useState('all');
  const [selectedInstitutions, setSelectedInstitutions] = useState<number[]>([]);
  const [bulkUpdateLoading, setBulkUpdateLoading] = useState(false);
  const [newDueDate, setNewDueDate] = useState<Date | undefined>(undefined);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const fetchInstitutions = useCallback(async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        search: searchTerm,
        status: statusFilter,
        plan: planFilter,
        limit: '100'
      });

      const response = await fetch(`/api/subscriptions?${params}`);
      const data: ApiResponse = await response.json();

      if (data.success && data.data) {
        setInstitutions(data.data.institutions);
        setSummary(data.data.summary);
      } else {
        toast.error(data.error || 'Failed to fetch subscription data');
      }
    } catch (error) {
      console.error('Error fetching institutions:', error);
      toast.error('Failed to fetch subscription data');
    } finally {
      setLoading(false);
    }
  }, [searchTerm, statusFilter, planFilter]);

  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      fetchInstitutions();
    }, 300);

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [fetchInstitutions]);

  const handleBulkStatusUpdate = async (status: 'paid' | 'unpaid') => {
    if (selectedInstitutions.length === 0) {
      toast.error('Please select institutions to update');
      return;
    }

    try {
      setBulkUpdateLoading(true);
      const response = await fetch('/api/subscriptions', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          institutionIds: selectedInstitutions,
          paymentStatus: status,
          paymentDueDate: newDueDate ? format(newDueDate, 'yyyy-MM-dd') : null
        }),
      });

      const data: ApiResponse = await response.json();

      if (data.success) {
        toast.success(`Successfully updated ${selectedInstitutions.length} institution(s)`);
        setSelectedInstitutions([]);
        setNewDueDate(undefined);
        fetchInstitutions();
      } else {
        toast.error(data.error || 'Failed to update payment status');
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      toast.error('Failed to update payment status');
    } finally {
      setBulkUpdateLoading(false);
    }
  };

  const toggleInstitutionSelection = (institutionId: number) => {
    setSelectedInstitutions(prev => 
      prev.includes(institutionId)
        ? prev.filter(id => id !== institutionId)
        : [...prev, institutionId]
    );
  };

  const toggleSelectAll = () => {
    if (selectedInstitutions.length === institutions.length) {
      setSelectedInstitutions([]);
    } else {
      setSelectedInstitutions(institutions.map(inst => inst.id));
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge variant="default" className="bg-green-100 text-green-800">Paid</Badge>;
      case 'unpaid':
        return <Badge variant="destructive">Unpaid</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const getPlanBadge = (plan: string) => {
    const colors = {
      basic: 'bg-blue-100 text-blue-800',
      standard: 'bg-purple-100 text-purple-800',
      premium: 'bg-orange-100 text-orange-800'
    };
    return (
      <Badge variant="outline" className={colors[plan as keyof typeof colors] || 'bg-gray-100 text-gray-800'}>
        {plan.charAt(0).toUpperCase() + plan.slice(1)}
      </Badge>
    );
  };

  const isOverdue = (dueDate: string | null) => {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date();
  };

  const formatDueDate = (dueDate: string | null) => {
    if (!dueDate) return 'No due date';
    const date = new Date(dueDate);
    const isOverdueDate = isOverdue(dueDate);
    return (
      <span className={isOverdueDate ? 'text-red-600 font-medium' : 'text-gray-600'}>
        {format(date, 'MMM dd, yyyy')}
        {isOverdueDate && ' (Overdue)'}
      </span>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Billing &amp; Subscriptions</h1>
          <p className="text-muted-foreground">
            Manage institution billing, payment status, and subscription details
          </p>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Institutions</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalInstitutions}</div>
            <p className="text-xs text-muted-foreground">
              {summary.totalStudents} students, {summary.totalTeachers} teachers
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Paid Institutions</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.paidInstitutions}</div>
            <p className="text-xs text-muted-foreground">
              {summary.totalInstitutions > 0 ? Math.round((summary.paidInstitutions / summary.totalInstitutions) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unpaid Institutions</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{summary.unpaidInstitutions}</div>
            <p className="text-xs text-muted-foreground">
              {summary.totalInstitutions > 0 ? Math.round((summary.unpaidInstitutions / summary.totalInstitutions) * 100) : 0}% of total
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Overdue Payments</CardTitle>
            <AlertTriangle className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{summary.overdueInstitutions}</div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Bulk Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Institution Billing</CardTitle>
          <CardDescription>
            View and manage payment status for all institutions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search institutions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Payment Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="unpaid">Unpaid</SelectItem>
              </SelectContent>
            </Select>
            <Select value={planFilter} onValueChange={setPlanFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Subscription Plan" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Plans</SelectItem>
                <SelectItem value="basic">Basic</SelectItem>
                <SelectItem value="standard">Standard</SelectItem>
                <SelectItem value="premium">Premium</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {selectedInstitutions.length > 0 && (
            <div className="flex flex-wrap items-center gap-4 mb-4 p-4 bg-blue-50 rounded-lg">
              <span className="text-sm font-medium">
                {selectedInstitutions.length} institution(s) selected
              </span>
              <div className="flex items-center gap-2">
                <Popover open={showDatePicker} onOpenChange={setShowDatePicker}>
                  <PopoverTrigger asChild>
                    <Button variant="outline" size="sm">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {newDueDate ? format(newDueDate, 'MMM dd, yyyy') : 'Set Due Date'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={newDueDate}
                      onSelect={(date) => {
                        setNewDueDate(date);
                        setShowDatePicker(false);
                      }}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <Button
                  size="sm"
                  onClick={() => handleBulkStatusUpdate('paid')}
                  disabled={bulkUpdateLoading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Mark as Paid
                </Button>
                <Button
                  size="sm"
                  variant="destructive"
                  onClick={() => handleBulkStatusUpdate('unpaid')}
                  disabled={bulkUpdateLoading}
                >
                  Mark as Unpaid
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedInstitutions([])}
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          )}

          {/* Institutions Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox
                      checked={selectedInstitutions.length === institutions.length && institutions.length > 0}
                      onCheckedChange={toggleSelectAll}
                    />
                  </TableHead>
                  <TableHead>Institution</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Billing Cycle</TableHead>
                  <TableHead>Payment Status</TableHead>
                  <TableHead>Due Date</TableHead>
                  <TableHead>Students/Teachers</TableHead>
                  <TableHead className="w-12"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      Loading institutions...
                    </TableCell>
                  </TableRow>
                ) : institutions.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} className="text-center py-8">
                      No institutions found
                    </TableCell>
                  </TableRow>
                ) : (
                  institutions.map((institution) => (
                    <TableRow key={institution.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedInstitutions.includes(institution.id)}
                          onCheckedChange={() => toggleInstitutionSelection(institution.id)}
                        />
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{institution.name}</div>
                          <div className="text-sm text-muted-foreground">{institution.type}</div>
                        </div>
                      </TableCell>
                      <TableCell>{getPlanBadge(institution.subscription_plan)}</TableCell>
                      <TableCell className="capitalize">{institution.billing_cycle}</TableCell>
                      <TableCell>{getStatusBadge(institution.payment_status)}</TableCell>
                      <TableCell>{formatDueDate(institution.payment_due_date)}</TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{institution.actual_student_count}/{institution.student_count} students</div>
                          <div className="text-muted-foreground">{institution.actual_teacher_count}/{institution.teacher_count} teachers</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedInstitutions([institution.id]);
                                handleBulkStatusUpdate('paid');
                              }}
                              className="text-green-600"
                            >
                              Mark as Paid
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setSelectedInstitutions([institution.id]);
                                handleBulkStatusUpdate('unpaid');
                              }}
                              className="text-red-600"
                            >
                              Mark as Unpaid
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}