# Implementasi Course Assignment ke Class - Dokumentasi Teknis

## 1. Analisis Sistem Saat Ini

### 1.1 Database Schema yang Sudah Ada

Sistem sudah memiliki infrastruktur database yang mendukung course assignment ke class:

```sql
-- Tabel courseEnrollments sudah ada
CREATE TABLE "course_enrollments" (
	"id" serial PRIMARY KEY NOT NULL,
	"course_id" integer NOT NULL,
	"class_id" integer NOT NULL,
	"enrolled_at" timestamp DEFAULT now() NOT NULL
);

-- Foreign key constraints
ALTER TABLE "course_enrollments" ADD CONSTRAINT "course_enrollments_course_id_courses_id_fk" 
  FOREIGN KEY ("course_id") REFERENCES "public"."courses"("id") ON DELETE no action ON UPDATE no action;
ALTER TABLE "course_enrollments" ADD CONSTRAINT "course_enrollments_class_id_classes_id_fk" 
  FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE no action ON UPDATE no action;
```

### 1.2 API Endpoints yang Sudah Ada

API `/api/enrollments` sudah mendukung course assignment:

**GET /api/enrollments**
- Parameter: `type=course`, `teacherId`, `courseId` (optional), `classId` (optional)
- Fungsi: Mengambil daftar course enrollments

**POST /api/enrollments**
- Parameter: `type=course`, `courseId`, `classId`, `teacherId`
- Fungsi: Assign course ke class
- Validasi: Teacher ownership, institution matching, duplicate prevention

### 1.3 UI yang Sudah Ada

- **Teacher Dashboard**: Link ke enrollments page
- **Class Detail Page**: Quick action "Assign Courses" yang mengarah ke enrollments page
- **Enrollments Page**: Interface untuk mengelola course assignments

## 2. Spesifikasi Teknis Lengkap

### 2.1 Database Schema Detail

#### Tabel Utama
```typescript
// courseEnrollments table (sudah ada)
export const courseEnrollments = pgTable('course_enrollments', {
  id: serial('id').primaryKey(),
  courseId: integer('course_id')
    .references(() => courses.id)
    .notNull(),
  classId: integer('class_id')
    .references(() => classes.id)
    .notNull(),
  enrolledAt: timestamp('enrolled_at').defaultNow().notNull()
});
```

#### Relasi Database
```typescript
// Relations (sudah ada)
export const courseEnrollmentsRelations = relations(
  courseEnrollments,
  ({ one }) => ({
    course: one(courses, {
      fields: [courseEnrollments.courseId],
      references: [courses.id]
    }),
    class: one(classes, {
      fields: [courseEnrollments.classId],
      references: [classes.id]
    })
  })
);
```

### 2.2 API Endpoints Specification

#### GET /api/enrollments
**Purpose**: Mengambil daftar course assignments

**Parameters**:
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| type | string | Yes | "course" untuk course assignments |
| teacherId | number | Yes | ID teacher yang meminta data |
| courseId | number | No | Filter berdasarkan course tertentu |
| classId | number | No | Filter berdasarkan class tertentu |

**Response**:
```json
{
  "enrollments": [
    {
      "id": 1,
      "courseId": 5,
      "classId": 3,
      "enrolledAt": "2024-01-15T10:30:00Z",
      "courseName": "Mathematics Grade 10",
      "courseCode": "MATH10A",
      "className": "Class 10A"
    }
  ]
}
```

#### POST /api/enrollments
**Purpose**: Assign course ke class

**Request Body**:
```json
{
  "type": "course",
  "courseId": 5,
  "classId": 3,
  "teacherId": 2
}
```

**Validasi**:
1. Teacher ownership verification
2. Institution matching (course dan class harus dari institution yang sama)
3. Duplicate assignment prevention
4. Course dan class existence check

**Response Success**:
```json
{
  "enrollment": {
    "id": 1,
    "courseId": 5,
    "classId": 3,
    "enrolledAt": "2024-01-15T10:30:00Z"
  },
  "message": "Course enrolled to class successfully"
}
```

#### DELETE /api/enrollments/[id]
**Purpose**: Menghapus course assignment dari class

**Parameters**:
- `id`: Course enrollment ID
- `teacherId`: Teacher ID untuk authorization

**Response**:
```json
{
  "success": true,
  "message": "Course assignment removed successfully"
}
```

### 2.3 TypeScript Types

```typescript
// Database types (sudah ada di database.ts)
export type CourseEnrollment = InferSelectModel<typeof courseEnrollments>;
export type NewCourseEnrollment = InferInsertModel<typeof courseEnrollments>;

// Extended types untuk UI
export type CourseEnrollmentWithDetails = CourseEnrollment & {
  course: Course;
  class: Class;
};

export type ClassWithCourses = Class & {
  courseEnrollments: (CourseEnrollment & { course: Course })[];
};

export type CourseWithClasses = Course & {
  courseEnrollments: (CourseEnrollment & { class: Class })[];
};
```

## 3. UI Specifications

### 3.1 Course Assignment Interface

#### Lokasi: `/dashboard/teacher/enrollments`

**Layout Structure**:
```
┌─────────────────────────────────────────────────────────────┐
│ Course Assignment Management                                │
├─────────────────────────────────────────────────────────────┤
│ [Search Box]                                    [+ Assign]  │
├─────────────────────────────────────────────────────────────┤
│ Active Course Assignments                                   │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Course Name    │ Class Name    │ Assigned Date │ Actions │ │
│ │ Mathematics    │ Grade 10A     │ Jan 15, 2024  │ Remove  │ │
│ │ Physics        │ Grade 10A     │ Jan 10, 2024  │ Remove  │ │
│ └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### Assign Course Dialog
```
┌─────────────────────────────────────────────────────────────┐
│ Assign Course to Class                                      │
├─────────────────────────────────────────────────────────────┤
│ Select Course:                                              │
│ [Dropdown: My Courses]                                      │
│                                                             │
│ Select Class:                                               │
│ [Dropdown: My Classes]                                      │
│                                                             │
│                                    [Cancel] [Assign Course] │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 Class Detail Enhancement

#### Lokasi: `/dashboard/teacher/classes/[id]`

**Tambahan Section**:
```jsx
{/* Assigned Courses Card */}
<Card>
  <CardHeader>
    <CardTitle>Assigned Courses</CardTitle>
    <CardDescription>Courses assigned to this class</CardDescription>
  </CardHeader>
  <CardContent>
    <div className="space-y-2">
      {assignedCourses.map(course => (
        <div key={course.id} className="flex justify-between items-center p-3 border rounded">
          <div>
            <h4 className="font-medium">{course.name}</h4>
            <p className="text-sm text-muted-foreground">{course.courseCode}</p>
          </div>
          <Button variant="outline" size="sm" onClick={() => removeCourse(course.id)}>
            Remove
          </Button>
        </div>
      ))}
    </div>
    <Button className="mt-4" onClick={() => setShowAssignDialog(true)}>
      <Plus className="mr-2 h-4 w-4" />
      Assign Course
    </Button>
  </CardContent>
</Card>
```

### 3.3 Course Detail Enhancement

#### Lokasi: `/dashboard/teacher/courses/[id]`

**Tambahan Section**:
```jsx
{/* Assigned Classes Card */}
<Card>
  <CardHeader>
    <CardTitle>Assigned Classes</CardTitle>
    <CardDescription>Classes where this course is assigned</CardDescription>
  </CardHeader>
  <CardContent>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      {assignedClasses.map(classItem => (
        <div key={classItem.id} className="p-4 border rounded-lg">
          <h4 className="font-medium">{classItem.name}</h4>
          <p className="text-sm text-muted-foreground">
            {classItem.studentCount} students
          </p>
          <p className="text-xs text-muted-foreground">
            Assigned: {new Date(classItem.enrolledAt).toLocaleDateString()}
          </p>
        </div>
      ))}
    </div>
  </CardContent>
</Card>
```

## 4. Implementation Plan

### 4.1 Backend Implementation (Sudah Selesai ✅)

- [x] Database schema courseEnrollments
- [x] API endpoints /api/enrollments
- [x] Validasi dan authorization
- [x] Error handling

### 4.2 Frontend Implementation (Perlu Dikembangkan)

#### Phase 1: Core Functionality
1. **Enhance Enrollments Page** (`/dashboard/teacher/enrollments`)
   - Tambah tab untuk "Course Assignments"
   - Interface untuk assign/remove courses dari classes
   - Search dan filter functionality

2. **Course Assignment Dialog Component**
   - Reusable dialog untuk assign courses
   - Dropdown untuk course dan class selection
   - Form validation dan error handling

#### Phase 2: Enhanced UI
3. **Class Detail Page Enhancement**
   - Section untuk menampilkan assigned courses
   - Quick assign/remove functionality

4. **Course Detail Page Enhancement**
   - Section untuk menampilkan assigned classes
   - Statistics dan analytics

#### Phase 3: Advanced Features
5. **Bulk Assignment**
   - Assign multiple courses ke multiple classes
   - Import/export functionality

6. **Analytics dan Reporting**
   - Course assignment statistics
   - Class utilization reports

### 4.3 Component Structure

```
src/components/
├── course-assignment/
│   ├── assign-course-dialog.tsx
│   ├── course-assignment-table.tsx
│   ├── class-courses-section.tsx
│   └── course-classes-section.tsx
└── ui/
    └── (existing components)
```

## 5. API Usage Examples

### 5.1 Fetch Course Assignments
```typescript
// Get all course assignments for a teacher
const fetchCourseAssignments = async (teacherId: number) => {
  const response = await fetch(`/api/enrollments?type=course&teacherId=${teacherId}`);
  const data = await response.json();
  return data.enrollments;
};

// Get assignments for specific course
const fetchCourseAssignments = async (teacherId: number, courseId: number) => {
  const response = await fetch(`/api/enrollments?type=course&teacherId=${teacherId}&courseId=${courseId}`);
  const data = await response.json();
  return data.enrollments;
};
```

### 5.2 Assign Course to Class
```typescript
const assignCourseToClass = async (courseId: number, classId: number, teacherId: number) => {
  const response = await fetch('/api/enrollments', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      type: 'course',
      courseId,
      classId,
      teacherId
    })
  });
  
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error || 'Failed to assign course');
  }
  
  return data;
};
```

### 5.3 Remove Course Assignment
```typescript
const removeCourseAssignment = async (enrollmentId: number, teacherId: number) => {
  const response = await fetch(`/api/enrollments/${enrollmentId}`, {
    method: 'DELETE',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ teacherId })
  });
  
  const data = await response.json();
  if (!response.ok) {
    throw new Error(data.error || 'Failed to remove assignment');
  }
  
  return data;
};
```

## 6. Security Considerations

### 6.1 Authorization
- Teacher hanya bisa assign courses yang mereka miliki
- Teacher hanya bisa assign ke classes di institution yang sama
- Validasi ownership di setiap API call

### 6.2 Data Validation
- Course dan class existence validation
- Institution matching validation
- Duplicate assignment prevention
- Input sanitization

### 6.3 Error Handling
- Comprehensive error messages
- Proper HTTP status codes
- Client-side validation
- Graceful fallbacks

## 7. Testing Strategy

### 7.1 Unit Tests
- API endpoint testing
- Database query testing
- Validation logic testing

### 7.2 Integration Tests
- End-to-end assignment flow
- Authorization testing
- Error scenario testing

### 7.3 UI Tests
- Component rendering tests
- User interaction tests
- Form validation tests

## 8. Performance Considerations

### 8.1 Database Optimization
- Proper indexing pada foreign keys
- Query optimization untuk joins
- Pagination untuk large datasets

### 8.2 Frontend Optimization
- Lazy loading untuk large lists
- Debounced search
- Optimistic updates
- Caching strategies

## 9. Deployment Checklist

### 9.1 Database
- [x] Migration scripts sudah dijalankan
- [x] Foreign key constraints sudah ada
- [x] Indexes sudah optimal

### 9.2 Backend
- [x] API endpoints sudah tested
- [x] Authorization sudah implemented
- [x] Error handling sudah comprehensive

### 9.3 Frontend
- [ ] UI components sudah developed
- [ ] Integration dengan API sudah tested
- [ ] User experience sudah optimal

## 10. Next Steps

Berdasarkan analisis ini, sistem sudah memiliki foundation yang kuat untuk course assignment ke class. Yang perlu dilakukan selanjutnya adalah:

1. **Immediate (High Priority)**:
   - Enhance enrollments page dengan course assignment interface
   - Buat assign course dialog component
   - Test end-to-end functionality

2. **Short Term (Medium Priority)**:
   - Enhance class dan course detail pages
   - Tambah analytics dan reporting
   - Improve user experience

3. **Long Term (Low Priority)**:
   - Bulk assignment features
   - Advanced filtering dan search
   - Mobile optimization

Sistem sudah siap untuk implementasi course assignment ke class dengan infrastruktur yang solid dan API yang lengkap.