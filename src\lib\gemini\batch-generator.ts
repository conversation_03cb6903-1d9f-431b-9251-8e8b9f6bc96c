import { generateChapter<PERSON>ontent, generateChapterContentLegacy } from './content-generator';
import { generateModuleQuiz, generateFinalExam, generateModuleQuizLegacy, generateFinalExamLegacy } from './quiz-generator';
import { 
  AICourseOutline, 
  AIGeneratedContent, 
  AIGeneratedQuiz, 
  GenerationProgress, 
  GenerationStep, 
  AIGenerationError,
  BatchGenerationResult
} from './types';

/**
 * Sequential batch generator for course content
 */
export class BatchContentGenerator {
  private steps: GenerationStep[] = [];
  private currentStepIndex = 0;
  private progress: GenerationProgress;
  private onProgressUpdate?: (progress: GenerationProgress) => void;
  private onStepComplete?: (step: GenerationStep, result: any) => void;
  private sessionId: string;
  private useChatSession: boolean;

  constructor(
    private courseOutline: AICourseOutline,
    onProgressUpdate?: (progress: GenerationProgress) => void,
    onStepComplete?: (step: GenerationStep, result: any) => void,
    sessionId?: string,
    useChatSession: boolean = true
  ) {
    this.onProgressUpdate = onProgressUpdate;
    this.onStepComplete = onStepComplete;
    this.sessionId = sessionId || `course-generation-${Date.now()}`;
    this.useChatSession = useChatSession;
    this.initializeSteps();
    this.progress = {
      currentStep: this.steps[0]?.name || 'Initializing',
      totalSteps: this.steps.length,
      completedSteps: 0,
      isGenerating: false
    };
  }

  /**
   * Initialize generation steps based on course outline
   */
  private initializeSteps(): void {
    this.steps = [];
    let stepId = 1;

    // Add steps for each module and its chapters
    this.courseOutline.modules.forEach((module, moduleIndex) => {
      module.chapters.forEach((chapter, chapterIndex) => {
        this.steps.push({
          id: `step-${stepId++}`,
          name: `${module.name} - ${chapter.name}`,
          type: 'chapter',
          status: 'pending',
          moduleIndex,
          chapterIndex
        });
      });

      // Add module quiz step if needed
      if (module.hasModuleQuiz) {
        this.steps.push({
          id: `step-${stepId++}`,
          name: `${module.name} - Module Quiz`,
          type: 'quiz',
          status: 'pending',
          moduleIndex
        });
      }
    });

    // Add final exam step if needed
    if (this.courseOutline.hasFinalExam) {
      this.steps.push({
        id: `step-${stepId++}`,
        name: 'Final Exam',
        type: 'final_exam',
        status: 'pending'
      });
    }
  }

  /**
   * Start the sequential generation process
   * @returns Promise<BatchGenerationResults> Complete generation results
   */
  async generateAll(): Promise<BatchGenerationResults> {
    const results: BatchGenerationResults = {
      chapters: new Map(),
      moduleQuizzes: new Map(),
      finalExam: null,
      errors: []
    };

    this.progress.isGenerating = true;
    this.updateProgress();

    try {
      for (let i = 0; i < this.steps.length; i++) {
        this.currentStepIndex = i;
        const step = this.steps[i];
        
        this.progress.currentStep = step.name;
        step.status = 'generating';
        this.updateProgress();

        try {
          const result = await this.generateStep(step);
          
          // Store result based on step type
          this.storeResult(step, result, results);
          
          step.status = 'completed';
          this.progress.completedSteps++;
          
          if (this.onStepComplete) {
            this.onStepComplete(step, result);
          }
        } catch (error) {
          step.status = 'error';
          const errorInfo = {
            step: step.name,
            error: error instanceof Error ? error.message : 'Unknown error',
            details: error
          };
          results.errors.push(errorInfo);
          
          // Continue with next step instead of failing completely
          console.error(`Failed to generate step: ${step.name}`, error);
        }

        this.updateProgress();
      }
    } finally {
      this.progress.isGenerating = false;
      this.progress.currentStep = 'Completed';
      this.updateProgress();
    }

    return results;
  }

  /**
   * Generate content for a specific step
   * @param step Generation step
   * @returns Promise<any> Generated content
   */
  private async generateStep(step: GenerationStep): Promise<any> {
    switch (step.type) {
      case 'chapter':
        return this.generateChapterStep(step);
      case 'quiz':
        return this.generateModuleQuizStep(step);
      case 'final_exam':
        return this.generateFinalExamStep();
      default:
        throw new AIGenerationError(
          `Unknown step type: ${step.type}`,
          'INVALID_STEP_TYPE'
        );
    }
  }

  /**
   * Generate chapter content
   * @param step Chapter generation step
   * @returns Promise<AIGeneratedContent> Generated chapter content
   */
  private async generateChapterStep(step: GenerationStep): Promise<AIGeneratedContent> {
    if (step.moduleIndex === undefined || step.chapterIndex === undefined) {
      throw new AIGenerationError(
        'Module and chapter indices are required for chapter generation',
        'MISSING_INDICES'
      );
    }

    const targetModule = this.courseOutline.modules[step.moduleIndex];
    const chapter = targetModule.chapters[step.chapterIndex];

    if (this.useChatSession) {
      return await generateChapterContent(
        chapter,
        targetModule,
        this.courseOutline,
        this.sessionId
      );
    } else {
      return await generateChapterContentLegacy(
        chapter,
        targetModule,
        this.courseOutline
      );
    }
  }

  /**
   * Generate module quiz
   * @param step Module quiz generation step
   * @returns Promise<AIGeneratedQuiz> Generated module quiz
   */
  private async generateModuleQuizStep(step: GenerationStep): Promise<AIGeneratedQuiz> {
    if (step.moduleIndex === undefined) {
      throw new AIGenerationError(
        'Module index is required for module quiz generation',
        'MISSING_MODULE_INDEX'
      );
    }

    const targetModule = this.courseOutline.modules[step.moduleIndex];
    if (this.useChatSession) {
      return await generateModuleQuiz(targetModule, this.courseOutline, this.sessionId);
    } else {
      return await generateModuleQuizLegacy(targetModule, this.courseOutline);
    }
  }

  /**
   * Generate final exam
   * @returns Promise<AIGeneratedQuiz> Generated final exam
   */
  private async generateFinalExamStep(): Promise<AIGeneratedQuiz> {
    if (this.useChatSession) {
      return await generateFinalExam(this.courseOutline, this.sessionId);
    } else {
      return await generateFinalExamLegacy(this.courseOutline);
    }
  }

  /**
   * Store generation result in the appropriate collection
   * @param step Generation step
   * @param result Generated content
   * @param results Results collection
   */
  private storeResult(step: GenerationStep, result: any, results: BatchGenerationResults): void {
    switch (step.type) {
      case 'chapter':
        if (step.moduleIndex !== undefined && step.chapterIndex !== undefined) {
          const key = `${step.moduleIndex}-${step.chapterIndex}`;
          results.chapters.set(key, result as AIGeneratedContent);
        }
        break;
      case 'quiz':
        if (step.moduleIndex !== undefined) {
          results.moduleQuizzes.set(step.moduleIndex, result as AIGeneratedQuiz);
        }
        break;
      case 'final_exam':
        results.finalExam = result as AIGeneratedQuiz;
        break;
    }
  }

  /**
   * Update progress and notify listeners
   */
  private updateProgress(): void {
    if (this.onProgressUpdate) {
      this.onProgressUpdate({ ...this.progress });
    }
  }

  /**
   * Get current progress
   * @returns GenerationProgress Current progress state
   */
  getProgress(): GenerationProgress {
    return { ...this.progress };
  }

  /**
   * Get all generation steps
   * @returns GenerationStep[] Array of all steps
   */
  getSteps(): GenerationStep[] {
    return [...this.steps];
  }

  /**
   * Cancel the generation process
   */
  cancel(): void {
    this.progress.isGenerating = false;
    this.progress.currentStep = 'Cancelled';
    this.updateProgress();
  }
}

/**
 * Results from batch generation
 */
export interface BatchGenerationResults {
  chapters: Map<string, AIGeneratedContent>; // key: "moduleIndex-chapterIndex"
  moduleQuizzes: Map<number, AIGeneratedQuiz>; // key: moduleIndex
  finalExam: AIGeneratedQuiz | null;
  errors: Array<{
    step: string;
    error: string;
    details: any;
  }>;
}

/**
 * Generate course content in batches with progress tracking
 * @param courseOutline Course outline to generate content for
 * @param onProgress Progress callback
 * @param onStepComplete Step completion callback
 * @returns Promise<BatchGenerationResults> Complete generation results
 */
export const generateCourseContentBatch = async (
  courseOutline: AICourseOutline,
  onProgress?: (progress: GenerationProgress) => void,
  onStepComplete?: (step: GenerationStep, result: any) => void,
  sessionId?: string
): Promise<BatchGenerationResults> => {
  const generator = new BatchContentGenerator(
    courseOutline,
    onProgress,
    onStepComplete,
    sessionId,
    true
  );

  return await generator.generateAll();
};

export const generateCourseContentBatchLegacy = async (
  courseOutline: AICourseOutline,
  onProgress?: (progress: GenerationProgress) => void,
  onStepComplete?: (step: GenerationStep, result: any) => void
): Promise<BatchGenerationResults> => {
  const generator = new BatchContentGenerator(
    courseOutline,
    onProgress,
    onStepComplete,
    undefined,
    false
  );

  return await generator.generateAll();
};

/**
 * Generate specific chapters in batch
 * @param chapters Array of chapter requests
 * @param courseContext Course context
 * @param onProgress Progress callback
 * @returns Promise<BatchGenerationResult[]> Array of generation results
 */
export const generateChaptersBatch = async (
  chapters: Array<{
    name: string;
    description: string;
    hasQuiz: boolean;
    moduleContext: any;
  }>,
  courseContext: AICourseOutline,
  onProgress?: (completed: number, total: number, current: string) => void
): Promise<BatchGenerationResult[]> => {
  const results: BatchGenerationResult[] = [];

  for (let i = 0; i < chapters.length; i++) {
    const chapter = chapters[i];
    
    if (onProgress) {
      onProgress(i, chapters.length, chapter.name);
    }

    try {
      const content = await generateChapterContentLegacy(
        {
          name: chapter.name,
          description: chapter.description,
          hasQuiz: chapter.hasQuiz
        },
        chapter.moduleContext,
        courseContext
      );

      results.push({
        chapterName: chapter.name,
        content,
        success: true
      });
    } catch (error) {
      results.push({
        chapterName: chapter.name,
        content: { content: [], quiz: undefined },
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  if (onProgress) {
    onProgress(chapters.length, chapters.length, 'Completed');
  }

  return results;
};

/**
 * Estimate generation time based on course outline
 * @param courseOutline Course outline
 * @returns Estimated time in minutes
 */
export const estimateGenerationTime = (courseOutline: AICourseOutline): number => {
  let totalSteps = 0;
  
  // Count chapters
  courseOutline.modules.forEach(module => {
    totalSteps += module.chapters.length;
    if (module.hasModuleQuiz) {
      totalSteps += 1;
    }
  });
  
  // Add final exam
  if (courseOutline.hasFinalExam) {
    totalSteps += 1;
  }
  
  // Estimate 2-3 minutes per step (chapter content takes longer)
  const avgTimePerStep = 2.5;
  return Math.ceil(totalSteps * avgTimePerStep);
};