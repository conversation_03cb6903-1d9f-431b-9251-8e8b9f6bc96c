import {
  Alert<PERSON>riangle,
  <PERSON>R<PERSON>,
  Award,
  BarChart3,
  BookOpen,
  Book,
  Bot,
  Building2,
  Check,
  ChevronLeft,
  ChevronRight,
  Command,
  CreditCard,
  File,
  FileText,
  HelpCircle,
  Image,
  Laptop,
  LayoutDashboard,
  Loader2,
  LogIn,
  ShoppingBag,
  Moon,
  MoreHorizontal,
  Pizza,
  Plus,
  Settings,
  Sun,
  Trash2,
  TrendingUp,
  User,
  UserCheck,
  UserCircle,
  UserCog,
  UserPlus,
  Users,
  UserX,
  X,
  Square,
  ExternalLink,
  Github,
  Twitter,
  type LucideProps
} from 'lucide-react';

import {
  Search01Icon as SearchIcon,
  GraduateMaleIcon as GraduationCapIcon
} from 'hugeicons-react';

export type Icon = React.ComponentType<LucideProps>;

export const Icons = {
  dashboard: LayoutDashboard,
  logo: Command,
  login: LogIn,
  close: X,
  product: ShoppingBag,
  spinner: Loader2,
  kanban: Square,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  trash: Trash2,
  employee: User<PERSON>,
  post: FileText,
  page: File,
  userPen: UserCog,
  user2: UserCircle,
  media: Image,
  settings: Settings,
  billing: CreditCard,
  ellipsis: MoreHorizontal,
  add: Plus,
  warning: AlertTriangle,
  user: User,
  arrowRight: ArrowRight,
  help: HelpCircle,
  pizza: Pizza,
  sun: Sun,
  moon: Moon,
  laptop: Laptop,
  github: Github,
  twitter: Twitter,
  check: Check,
  // LMS specific icons - migrated from Tabler to Lucide equivalents
  building: Building2,
  users: Users,
  userPlus: UserPlus,
  userCheck: UserCheck,
  creditCard: CreditCard,
  barChart: BarChart3,
  bookOpen: BookOpen,
  book: Book,
  bot: Bot,
  trendingUp: TrendingUp,
  award: Award,
  plus: Plus,
  // Additional LMS icons
  school: BookOpen,
  certificate: Award,
  enrollment: UserPlus,
  searchList: SearchIcon,
  graduationCap: GraduationCapIcon
};