import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Book, Hourglass, Award } from 'lucide-react';
import { CourseData, AcademicsData } from '../course-creation-wizard';

interface AcademicsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function AcademicsStep({ data, onUpdate }: AcademicsStepProps) {
  const academics = data.academics || { credits: 0, workload: '', assessment: [] };

  const handleUpdate = (field: keyof AcademicsData, value: string | number | string[]) => {
    onUpdate({
      academics: {
        ...academics,
        [field]: value,
      },
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Informasi Akademik</CardTitle>
        <CardDescription>Detail terkait struktur akademik dan penilaian kursus.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2">
          <Book className="h-5 w-5 text-gray-500" />
          <Label htmlFor="credits">Kredit</Label>
        </div>
        <Input
          id="credits"
          type="number"
          value={academics.credits}
          onChange={(e) => handleUpdate('credits', parseInt(e.target.value))}
          placeholder="Contoh: 12"
        />

        <div className="flex items-center space-x-2">
          <Hourglass className="h-5 w-5 text-gray-500" />
          <Label htmlFor="workload">Beban Kerja</Label>
        </div>
        <Input
          id="workload"
          type="text"
          value={academics.workload}
          onChange={(e) => handleUpdate('workload', e.target.value)}
          placeholder="Contoh: 12-15 jam/minggu"
        />

        <div className="flex items-center space-x-2">
          <Award className="h-5 w-5 text-gray-500" />
          <Label htmlFor="assessment">Penilaian (pisahkan dengan koma)</Label>
        </div>
        <Textarea
          id="assessment"
          value={academics.assessment.join(', ')}
          onChange={(e) => handleUpdate('assessment', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Ujian teori (25%), Studi kasus proyek (35%)"
        />
      </CardContent>
    </Card>
  );
}