import { generateWithGeminiStructured, fileToBase64, SchemaConfig, Type, getChatSession, sendStructuredChatMessage, ChatMessage } from './client';
import { AICourseOutline, AIGenerationConfig, AIGenerationError } from './types';

// Schema for course outline structured output
const courseOutlineSchema: SchemaConfig = {
  responseMimeType: 'application/json',
  responseSchema: {
    type: Type.OBJECT,
    properties: {
      courseName: {
        type: Type.STRING,
        description: 'Clear, descriptive course title'
      },
      description: {
        type: Type.STRING,
        description: 'Comprehensive course description (2-3 sentences)'
      },
      modules: {
        type: Type.ARRAY,
        items: {
          type: Type.OBJECT,
          properties: {
            name: {
              type: Type.STRING,
              description: 'Module title'
            },
            description: {
              type: Type.STRING,
              description: 'Module description (1-2 sentences)'
            },
            chapters: {
              type: Type.ARRAY,
              items: {
                type: Type.OBJECT,
                properties: {
                  name: {
                    type: Type.STRING,
                    description: 'Chapter title'
                  },
                  description: {
                    type: Type.STRING,
                    description: 'Chapter description (1 sentence)'
                  },
                  hasQuiz: {
                    type: Type.BOOLEAN,
                    description: 'Whether this chapter has a quiz'
                  }
                },
                required: ['name', 'description', 'hasQuiz']
              }
            },
            hasModuleQuiz: {
              type: Type.BOOLEAN,
              description: 'Whether this module has a quiz'
            }
          },
          required: ['name', 'description', 'chapters', 'hasModuleQuiz']
        }
      },
      hasFinalExam: {
        type: Type.BOOLEAN,
        description: 'Whether the course has a final exam'
      }
    },
    required: ['courseName', 'description', 'modules', 'hasFinalExam']
  }
};

/**
 * Generate course outline from PDF using Gemini AI with chat session
 * @param pdfFile PDF file to analyze
 * @param sessionId Chat session ID for maintaining context
 * @param config Optional configuration for course generation
 * @returns Promise<AICourseOutline> Generated course outline
 */
export const generateCourseOutlineFromPDF = async (
  pdfFile: File,
  sessionId: string,
  config?: AIGenerationConfig
): Promise<AICourseOutline> => {
  try {
    const base64Content = await fileToBase64(pdfFile);
    
    const prompt = createCourseOutlinePrompt(config);
    
    // Create or get existing chat session
    getChatSession(sessionId);
    
    // Prepare message with PDF content
    const message: ChatMessage = {
      role: 'user',
      parts: [
        {
          text: prompt
        },
        {
          inlineData: {
            mimeType: 'application/pdf',
            data: base64Content
          }
        }
      ]
    };

    const outline = await sendStructuredChatMessage<AICourseOutline>(sessionId, message, courseOutlineSchema);
    
    // Validate the generated outline
    validateCourseOutline(outline);
    
    return outline;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      'Failed to generate course outline from PDF',
      'OUTLINE_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate course outline from PDF using Gemini AI (backward compatibility)
 * @param pdfFile PDF file to analyze
 * @param config Optional configuration for course generation
 * @returns Promise<AICourseOutline> Generated course outline
 */
export const generateCourseOutlineFromPDFLegacy = async (
  pdfFile: File,
  config?: AIGenerationConfig
): Promise<AICourseOutline> => {
  try {
    const base64Content = await fileToBase64(pdfFile);
    
    const prompt = createCourseOutlinePrompt(config);
    
    const contents = [
      {
        role: 'user',
        parts: [
          {
            text: prompt
          },
          {
            inlineData: {
              mimeType: 'application/pdf',
              data: base64Content
            }
          }
        ]
      }
    ];

    const outline = await generateWithGeminiStructured<AICourseOutline>(contents, courseOutlineSchema);
    
    // Validate the generated outline
    validateCourseOutline(outline);
    
    return outline;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      'Failed to generate course outline from PDF',
      'OUTLINE_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Create prompt for course outline generation (simplified for structured output)
 * @param config Optional configuration
 * @returns Formatted prompt string
 */
export const createCourseOutlinePrompt = (config?: AIGenerationConfig): string => {
  const basePrompt = `
Analyze the provided document and create a comprehensive course outline.

Guidelines:
- Create 3-6 modules based on the content
- Each module should have 2-5 chapters
- Chapter names should be specific and actionable
- Every chapter MUST include a quiz
- Every module MUST include a module quiz
- Final exam should be included for comprehensive courses
- Ensure logical progression from basic to advanced concepts
`;

  let configPrompt = '';
  if (config) {
    configPrompt = `
Additional Requirements:
`;
    
    if (config.courseName) {
      configPrompt += `- Course name should be related to: "${config.courseName}"
`;
    }
    
    if (config.courseDescription) {
      configPrompt += `- Course should focus on: "${config.courseDescription}"
`;
    }
    
    if (config.targetAudience) {
      configPrompt += `- Target audience: ${config.targetAudience}
`;
    }
    
    if (config.difficulty) {
      configPrompt += `- Difficulty level: ${config.difficulty}
`;
    }
  }

  return basePrompt + configPrompt;
};

/**
 * Validate the generated course outline
 * @param outline Course outline to validate
 * @throws AIGenerationError if validation fails
 */
export const validateCourseOutline = (outline: AICourseOutline): void => {
  if (!outline.courseName || typeof outline.courseName !== 'string') {
    throw new AIGenerationError(
      'Invalid course outline: missing or invalid courseName',
      'VALIDATION_ERROR'
    );
  }

  if (!outline.description || typeof outline.description !== 'string') {
    throw new AIGenerationError(
      'Invalid course outline: missing or invalid description',
      'VALIDATION_ERROR'
    );
  }

  if (!Array.isArray(outline.modules) || outline.modules.length === 0) {
    throw new AIGenerationError(
      'Invalid course outline: modules must be a non-empty array',
      'VALIDATION_ERROR'
    );
  }

  outline.modules.forEach((module, moduleIndex) => {
    if (!module.name || typeof module.name !== 'string') {
      throw new AIGenerationError(
        `Invalid module at index ${moduleIndex}: missing or invalid name`,
        'VALIDATION_ERROR'
      );
    }

    if (!module.description || typeof module.description !== 'string') {
      throw new AIGenerationError(
        `Invalid module at index ${moduleIndex}: missing or invalid description`,
        'VALIDATION_ERROR'
      );
    }

    if (!Array.isArray(module.chapters) || module.chapters.length === 0) {
      throw new AIGenerationError(
        `Invalid module at index ${moduleIndex}: chapters must be a non-empty array`,
        'VALIDATION_ERROR'
      );
    }

    module.chapters.forEach((chapter, chapterIndex) => {
      if (!chapter.name || typeof chapter.name !== 'string') {
        throw new AIGenerationError(
          `Invalid chapter at module ${moduleIndex}, chapter ${chapterIndex}: missing or invalid name`,
          'VALIDATION_ERROR'
        );
      }

      if (!chapter.description || typeof chapter.description !== 'string') {
        throw new AIGenerationError(
          `Invalid chapter at module ${moduleIndex}, chapter ${chapterIndex}: missing or invalid description`,
          'VALIDATION_ERROR'
        );
      }

      if (typeof chapter.hasQuiz !== 'boolean') {
        throw new AIGenerationError(
          `Invalid chapter at module ${moduleIndex}, chapter ${chapterIndex}: hasQuiz must be boolean`,
          'VALIDATION_ERROR'
        );
      }
    });

    if (typeof module.hasModuleQuiz !== 'boolean') {
      throw new AIGenerationError(
        `Invalid module at index ${moduleIndex}: hasModuleQuiz must be boolean`,
        'VALIDATION_ERROR'
      );
    }
  });

  if (typeof outline.hasFinalExam !== 'boolean') {
    throw new AIGenerationError(
      'Invalid course outline: hasFinalExam must be boolean',
      'VALIDATION_ERROR'
    );
  }
};

/**
 * Generate course outline from text content with chat session
 * @param textContent Text content to analyze
 * @param sessionId Chat session ID for maintaining context
 * @param config Optional configuration for course generation
 * @returns Promise<AICourseOutline> Generated course outline
 */
export const generateCourseOutlineFromText = async (
  textContent: string,
  sessionId: string,
  config?: AIGenerationConfig
): Promise<AICourseOutline> => {
  try {
    const prompt = createCourseOutlinePrompt(config) + `\n\nContent to analyze:\n${textContent}`;
    
    // Create or get existing chat session
    getChatSession(sessionId);
    
    // Prepare message
    const message: ChatMessage = {
      role: 'user',
      parts: [{ text: prompt }]
    };

    const outline = await sendStructuredChatMessage<AICourseOutline>(sessionId, message, courseOutlineSchema);
    
    // Validate the generated outline
    validateCourseOutline(outline);
    
    return outline;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      'Failed to generate course outline from text',
      'OUTLINE_GENERATION_ERROR',
      error
    );
  }
};

/**
 * Generate course outline from text content (backward compatibility)
 * @param textContent Text content to analyze
 * @param config Optional configuration for course generation
 * @returns Promise<AICourseOutline> Generated course outline
 */
export const generateCourseOutlineFromTextLegacy = async (
  textContent: string,
  config?: AIGenerationConfig
): Promise<AICourseOutline> => {
  try {
    const prompt = createCourseOutlinePrompt(config) + `\n\nContent to analyze:\n${textContent}`;
    
    const contents = [
      {
        role: 'user',
        parts: [{ text: prompt }]
      }
    ];

    const outline = await generateWithGeminiStructured<AICourseOutline>(contents, courseOutlineSchema);
    
    // Validate the generated outline
    validateCourseOutline(outline);
    
    return outline;
  } catch (error) {
    if (error instanceof AIGenerationError) {
      throw error;
    }
    throw new AIGenerationError(
      'Failed to generate course outline from text',
      'OUTLINE_GENERATION_ERROR',
      error
    );
  }
};