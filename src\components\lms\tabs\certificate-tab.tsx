import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Award, CheckCircle, XCircle, Lock, Eye, Download } from 'lucide-react';
import { Course, Institution } from '@/types/lms';

interface CertificateTabProps {
  courseData: Course;
  institution: Institution;
  overallProgress: number;
  onGenerateCertificate: () => void;
  onShowCertificate: () => void;
  onDownloadPDF?: () => void;
}

export const CertificateTab: React.FC<CertificateTabProps> = ({
  courseData,
  institution,
  overallProgress,
  onGenerateCertificate,
  onShowCertificate,
  onDownloadPDF
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle
          className='flex items-center space-x-2'
          style={{ color: institution.certificateTemplate?.primaryColor }}
        >
          <Award className='h-6 w-6' />
          <span>Sertifikasi Profesional</span>
        </CardTitle>
      </CardHeader>
      <CardContent className='p-6'>
        {courseData.finalExam.attempts === 0 ? (
          /* User hasn't taken final exam yet */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-yellow-200 bg-gradient-to-r from-yellow-50 to-orange-50 p-8'>
              <Lock className='mx-auto mb-4 h-16 w-16 text-yellow-600' />
              <h3 className='mb-2 text-2xl font-bold text-yellow-800'>
                Belum Mengikuti Final Exam
              </h3>
              <p className='text-yellow-700'>
                Kamu belum mengikuti final exam. Selesaikan final exam terlebih dahulu untuk mendapatkan sertifikat.
              </p>
            </div>
          </div>
        ) : courseData.certificate.isEligible &&
        courseData.certificate.isGenerated ? (
          /* Certificate Generated */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>
              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />
              <h3 className='mb-2 text-2xl font-bold text-green-800'>
                Selamat!
              </h3>
              <p className='text-green-700'>
                Anda telah berhasil menyelesaikan kursus {courseData.name} dan
                memperoleh sertifikasi.
              </p>
              {courseData.certificate.completionDate && (
                <p className='mt-2 text-sm text-green-600'>
                  Diselesaikan pada:{' '}
                  {new Date(
                    courseData.certificate.completionDate
                  ).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className='flex justify-center space-x-4'>
              <Button
                onClick={onShowCertificate}
                className='bg-green-600 hover:bg-green-700'
              >
                <Eye className='mr-2 h-4 w-4' />
                Lihat Sertifikat
              </Button>
              <Button
                variant='outline'
                className='border-green-600 text-green-600 hover:bg-green-50'
                onClick={onDownloadPDF}
              >
                <Download className='mr-2 h-4 w-4' />
                Unduh PDF
              </Button>
            </div>
          </div>
        ) : courseData.certificate.isEligible ? (
          /* Certificate Ready - Show View and Download */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>
              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />
              <h3 className='mb-2 text-2xl font-bold text-green-800'>
                Selamat!
              </h3>
              <p className='text-green-700'>
                Anda telah berhasil menyelesaikan kursus {courseData.name} dan
                memperoleh sertifikasi.
              </p>
              {courseData.certificate.completionDate && (
                <p className='mt-2 text-sm text-green-600'>
                  Diselesaikan pada:{' '}
                  {new Date(
                    courseData.certificate.completionDate
                  ).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className='flex justify-center space-x-4'>
              <Button
                onClick={onShowCertificate}
                className='bg-green-600 hover:bg-green-700'
              >
                <Eye className='mr-2 h-4 w-4' />
                Lihat Sertifikat
              </Button>
              <Button
                variant='outline'
                className='border-green-600 text-green-600 hover:bg-green-50'
                onClick={onDownloadPDF}
              >
                <Download className='mr-2 h-4 w-4' />
                Unduh PDF
              </Button>
            </div>
          </div>
        ) : (
          /* Not Eligible Yet */
          <div className='space-y-6'>
            <div className='rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center'>
              <Lock className='mx-auto mb-4 h-16 w-16 text-gray-400' />
              <h3 className='mb-2 text-2xl font-bold text-gray-700'>
                Persyaratan Sertifikat
              </h3>
              <p className='mb-4 text-gray-600'>
                Selesaikan semua persyaratan kursus untuk memperoleh sertifikasi
                profesional Anda.
              </p>
            </div>

            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-3'>
                <h4 className='font-medium text-gray-900'>Penyelesaian Modul</h4>
                {courseData.modules.map((module) => (
                  <div
                    key={module.id}
                    className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
                  >
                    <span className='text-sm'>{module.title}</span>
                    {module.moduleQuiz.isPassed ? (
                      <CheckCircle className='h-5 w-5 text-green-600' />
                    ) : (
                      <XCircle className='h-5 w-5 text-gray-400' />
                    )}
                  </div>
                ))}
              </div>
              <div className='space-y-3'>
                <h4 className='font-medium text-gray-900'>
                  Persyaratan Akhir
                </h4>
                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                  <span className='text-sm'>
                    Final Exam (Min. {courseData.finalExam.minimumScore}%)
                  </span>
                  {courseData.finalExam.isPassed ? (
                    <CheckCircle className='h-5 w-5 text-green-600' />
                  ) : (
                    <XCircle className='h-5 w-5 text-gray-400' />
                  )}
                </div>
                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                  <span className='text-sm'>Skor Keseluruhan (Min. 70%)</span>
                  {overallProgress >= 70 ? (
                    <CheckCircle className='h-5 w-5 text-green-600' />
                  ) : (
                    <XCircle className='h-5 w-5 text-gray-400' />
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
