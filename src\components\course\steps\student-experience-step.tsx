import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, X, MessageSquare, HardHat, LifeBuoy } from 'lucide-react';
import { CourseData, StudentExperienceData } from '../course-creation-wizard';

interface StudentExperienceStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function StudentExperienceStep({ data, onUpdate }: StudentExperienceStepProps) {
  const studentExperience = data.studentExperience || { testimonials: [], facilities: [], support: [] };

  const handleUpdate = (field: keyof StudentExperienceData, value: string | string[] | { name: string; feedback: string }[]) => {
    onUpdate({
      studentExperience: {
        ...studentExperience,
        [field]: value,
      },
    });
  };

  const addTestimonial = () => {
    handleUpdate('testimonials', [...studentExperience.testimonials, { name: '', feedback: '' }]);
  };

  const updateTestimonial = (index: number, field: 'name' | 'feedback', value: string) => {
    const updatedTestimonials = [...studentExperience.testimonials];
    updatedTestimonials[index] = { ...updatedTestimonials[index], [field]: value };
    handleUpdate('testimonials', updatedTestimonials);
  };

  const removeTestimonial = (index: number) => {
    const updatedTestimonials = studentExperience.testimonials.filter((_, i) => i !== index);
    handleUpdate('testimonials', updatedTestimonials);
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Pengalaman Mahasiswa</CardTitle>
        <CardDescription>Detail terkait pengalaman, fasilitas, dan dukungan yang akan didapat mahasiswa.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <MessageSquare className="h-5 w-5 text-gray-500" />
            <Label>Testimoni</Label>
          </div>
          {studentExperience.testimonials.map((testimonial, index) => (
            <div key={index} className="flex items-end space-x-2 mb-4">
              <div className="flex-grow space-y-2">
                <Input
                  placeholder="Nama"
                  value={testimonial.name}
                  onChange={(e) => updateTestimonial(index, 'name', e.target.value)}
                />
                <Textarea
                  placeholder="Umpan Balik"
                  value={testimonial.feedback}
                  onChange={(e) => updateTestimonial(index, 'feedback', e.target.value)}
                />
              </div>
              <Button variant="destructive" size="icon" onClick={() => removeTestimonial(index)}>
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
          <Button variant="outline" onClick={addTestimonial}>
            <Plus className="h-4 w-4 mr-2" /> Tambah Testimoni
          </Button>
        </div>

        <div>
          <div className="flex items-center space-x-2 mb-2">
            <HardHat className="h-5 w-5 text-gray-500" />
            <Label htmlFor="facilities">Fasilitas (pisahkan dengan koma)</Label>
          </div>
          <Textarea
            id="facilities"
            value={studentExperience.facilities.join(', ')}
            onChange={(e) => handleUpdate('facilities', e.target.value.split(',').map(s => s.trim()))}
            placeholder="Contoh: Platform pembelajaran online, Studio desain virtual"
          />
        </div>

        <div>
          <div className="flex items-center space-x-2 mb-2">
            <LifeBuoy className="h-5 w-5 text-gray-500" />
            <Label htmlFor="support">Dukungan (pisahkan dengan koma)</Label>
          </div>
          <Textarea
            id="support"
            value={studentExperience.support.join(', ')}
            onChange={(e) => handleUpdate('support', e.target.value.split(',').map(s => s.trim()))}
            placeholder="Contoh: Instruktur kursus khusus, Forum diskusi rekan"
          />
        </div>
      </CardContent>
    </Card>
  );
}