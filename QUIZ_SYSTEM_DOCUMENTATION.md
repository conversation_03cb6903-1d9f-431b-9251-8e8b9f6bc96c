# Quiz System Documentation - Terang LMS

## 📋 Overview

The Terang LMS features a comprehensive, integrated quiz system that follows the structure outlined in plan.md. Quizzes are not standalone entities but are deeply integrated into the course structure, providing a progressive learning experience.

## 🏗️ Quiz Structure

### **Three-Tier Quiz System**

```
Course
├── Module 1
│   ├── Chapter 1 → Chapter Quiz
│   ├── Chapter 2 → Chapter Quiz  
│   ├── Chapter 3 → Chapter Quiz
│   └── Module Quiz (covers all chapters)
├── Module 2
│   ├── Chapter 4 → Chapter Quiz
│   ├── Chapter 5 → Chapter Quiz
│   └── Module Quiz (covers all chapters)
└── Final Exam (covers entire course)
```

### **1. Chapter Quizzes**
- **Purpose**: Test understanding of individual chapter content
- **Default Timing**: Available after completing chapter content
- **Duration**: 15-20 minutes
- **Questions**: 5-10 questions
- **Minimum Score**: 70%
- **Question Types**: Multiple choice, True/False, Short essay

### **2. Module Quizzes**
- **Purpose**: Comprehensive assessment of entire module
- **Default Timing**: Available after completing ALL chapters in module + their quizzes
- **Duration**: 30-45 minutes
- **Questions**: 10-15 questions
- **Minimum Score**: 75%
- **Question Types**: More complex questions, application-based

### **3. Final Exam**
- **Purpose**: Comprehensive assessment of entire course
- **Default Timing**: Available after completing ALL modules + their quizzes
- **Duration**: 60-90 minutes
- **Questions**: 20-30 questions
- **Minimum Score**: 80%
- **Question Types**: Advanced questions testing knowledge and application

## 🤖 AI-Generated Quiz Content

### **Automatic Quiz Generation**
When a course is generated from a PDF using Gemini AI, the system automatically creates:

1. **Chapter Quizzes**: Based on specific chapter content
2. **Module Quizzes**: Based on combined chapter content within the module
3. **Final Exam**: Based on entire course content

### **Quiz Generation Process**
```typescript
// 1. Process PDF with Gemini
const extractedText = await processPDFWithGemini(file);

// 2. Generate course outline with quiz structure
const outline = await generateCourseOutline(extractedText, courseInfo);

// 3. For each chapter, generate content + quiz
const chapterContent = await generateChapterContent(
  chapterName, chapterDescription, moduleContext, courseContext
);

// 4. For each module, generate comprehensive quiz
const moduleQuiz = await generateModuleQuiz(
  moduleName, moduleDescription, chapters, courseContext
);

// 5. Generate final exam covering all modules
const finalExam = await generateFinalExam(
  courseName, courseDescription, modules, courseContext
);
```

## 🔄 Progressive Unlocking System

### **Student Learning Flow**
1. **Start Module**: Student begins with first chapter
2. **Complete Chapter**: Read content, understand concepts
3. **Take Chapter Quiz**: Must pass to proceed (70% minimum)
4. **Repeat**: Continue with next chapter
5. **Module Complete**: After all chapters + quizzes completed
6. **Take Module Quiz**: Comprehensive assessment (75% minimum)
7. **Course Progress**: Repeat for all modules
8. **Final Exam**: Available after all modules completed (80% minimum)
9. **Certificate**: Generated upon final exam completion

### **Unlocking Rules**
- **Chapter Quiz**: Unlocked after reading chapter content
- **Next Chapter**: Unlocked after passing previous chapter quiz
- **Module Quiz**: Unlocked after passing ALL chapter quizzes in module
- **Next Module**: Unlocked after passing module quiz
- **Final Exam**: Unlocked after passing ALL module quizzes
- **Certificate**: Generated after passing final exam

## 💾 Database Schema

### **Quiz Types**
```sql
-- Quiz types stored in quizzes table
quizType ENUM('chapter', 'module', 'final')

-- Relationships
- Chapter Quiz: linked to specific chapter
- Module Quiz: linked to specific module  
- Final Exam: linked to course only
```

### **Key Tables**
```sql
quizzes (
  id, name, description,
  courseId, chapterId, moduleId,
  quizType, timeLimit, minimumScore,
  isActive, createdAt, updatedAt
)

questions (
  id, quizId, type, question,
  options, correctAnswer, points
)

quiz_attempts (
  id, quizId, studentId, score,
  passed, startedAt, completedAt
)
```

## 🎯 Question Types

### **1. Multiple Choice**
```json
{
  "type": "multiple_choice",
  "question": "What is the main concept in algebra?",
  "options": ["Variables", "Numbers", "Geometry", "Calculus"],
  "correctAnswer": "Variables",
  "points": 1
}
```

### **2. True/False**
```json
{
  "type": "true_false", 
  "question": "Algebra involves working with unknown variables.",
  "correctAnswer": "true",
  "points": 1
}
```

### **3. Essay Questions**
```json
{
  "type": "essay",
  "question": "Explain the importance of variables in algebraic expressions.",
  "correctAnswer": "Sample answer with key points for manual grading",
  "points": 5
}
```

## 📊 Scoring System

### **Chapter Quizzes**
- **Points**: 1-2 points per question
- **Total**: 5-20 points
- **Passing**: 70% minimum
- **Retakes**: Unlimited (with different questions)

### **Module Quizzes**  
- **Points**: 2-5 points per question
- **Total**: 20-75 points
- **Passing**: 75% minimum
- **Retakes**: Limited (2-3 attempts)

### **Final Exam**
- **Points**: 3-10 points per question
- **Total**: 60-300 points  
- **Passing**: 80% minimum
- **Retakes**: Very limited (1-2 attempts)

## 🔧 Implementation Features

### **For Teachers**
- **Automatic Generation**: Quizzes created during course generation
- **Manual Editing**: Can modify AI-generated questions
- **Analytics**: Track student performance across all quiz levels
- **Flexible Settings**: Adjust time limits, minimum scores, retake policies

### **For Students**
- **Progressive Access**: Quizzes unlock as content is completed
- **Immediate Feedback**: Instant results for objective questions
- **Progress Tracking**: Visual progress through quiz hierarchy
- **Retake Options**: Multiple attempts with different question sets

### **For Admins**
- **System Analytics**: Overall quiz performance metrics
- **Quality Control**: Monitor quiz difficulty and pass rates
- **Content Management**: Oversee quiz content across institutions

## 🚀 Benefits of This System

1. **Structured Learning**: Clear progression path for students
2. **Comprehensive Assessment**: Multiple levels of evaluation
3. **AI-Powered**: Automatic generation saves teacher time
4. **Adaptive**: Different difficulty levels for different quiz types
5. **Motivating**: Progressive unlocking keeps students engaged
6. **Thorough**: Ensures mastery before advancement
7. **Scalable**: Works for any course size or complexity

## 📈 Future Enhancements

- **Adaptive Questioning**: AI adjusts difficulty based on performance
- **Question Banks**: Reusable question pools for similar topics
- **Peer Review**: Student-generated questions and peer assessment
- **Advanced Analytics**: ML-powered insights into learning patterns
- **Mobile Optimization**: Enhanced mobile quiz-taking experience

This integrated quiz system ensures that learning is progressive, comprehensive, and thoroughly assessed at every level of the course structure.
