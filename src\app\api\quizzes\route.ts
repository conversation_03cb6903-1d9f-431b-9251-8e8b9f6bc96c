import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { quizzes, questions, chapters, modules, courses, users } from '@/lib/db/schema';
import { eq, and, or } from 'drizzle-orm';

// GET /api/quizzes - Get quizzes for a chapter or course
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const chapterId = searchParams.get('chapterId');
    const moduleId = searchParams.get('moduleId');
    const courseId = searchParams.get('courseId');
    const quizType = searchParams.get('quizType');
    const teacherId = searchParams.get('teacherId');
    
    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    let whereConditions = []; // Array to hold multiple conditions
    let accessValidated = false;
    
    if (chapterId) {
      // Get quizzes for a specific chapter
      const chapterWithCourse = await db
        .select({
          chapterId: chapters.id,
          moduleId: chapters.moduleId,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(chapters)
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(chapters.id, parseInt(chapterId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (chapterWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Chapter not found or access denied' },
          { status: 403 }
        );
      }

      whereConditions.push(eq(quizzes.chapterId, parseInt(chapterId)));
      accessValidated = true;
    } else if (moduleId) {
      // Get quizzes for a specific module
      const moduleWithCourse = await db
        .select({
          moduleId: modules.id,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(modules)
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(modules.id, parseInt(moduleId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (moduleWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Module not found or access denied' },
          { status: 403 }
        );
      }

      whereConditions.push(eq(quizzes.moduleId, parseInt(moduleId)));
      accessValidated = true;
    } else if (courseId) {
      // Get quizzes for a specific course
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, parseInt(courseId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Course not found or access denied' },
          { status: 403 }
        );
      }

      // If quizType is specified, filter by type
      if (quizType) {
        whereConditions.push(eq(quizzes.quizType, quizType));
        if (quizType === 'final') {
          whereConditions.push(eq(quizzes.courseId, parseInt(courseId)));
        }
      }
      
      accessValidated = true;
    } else {
      // Get all quizzes for teacher's courses
      const teacherCourses = await db
        .select({ id: courses.id })
        .from(courses)
        .where(eq(courses.teacherId, parseInt(teacherId)));
      
      const courseIds = teacherCourses.map(c => c.id);
      if (courseIds.length === 0) {
        return NextResponse.json({ quizzes: [] });
      }

      accessValidated = true;
    }

    if (!accessValidated) {
      return NextResponse.json(
        { error: 'Access denied' },
        { status: 403 }
      );
    }

    // Build the where condition
    let whereCondition;
    if (whereConditions.length === 1) {
      whereCondition = whereConditions[0];
    } else if (whereConditions.length > 1) {
      whereCondition = and(...whereConditions);
    } else {
      // For getting all teacher's quizzes, we'll filter in the main query
      whereCondition = eq(courses.teacherId, parseInt(teacherId));
    }

    // Get quizzes with related information based on quiz type
    let quizList;
    
    if (whereConditions.length > 0) {
      // Specific query based on conditions
      quizList = await db
        .select({
          id: quizzes.id,
          name: quizzes.name,
          description: quizzes.description,
          quizType: quizzes.quizType,
          timeLimit: quizzes.timeLimit,
          minimumScore: quizzes.minimumScore,
          isActive: quizzes.isActive,
          chapterId: quizzes.chapterId,
          moduleId: quizzes.moduleId,
          courseId: quizzes.courseId,
          createdAt: quizzes.createdAt,
          updatedAt: quizzes.updatedAt,
          chapterName: chapters.name,
          moduleName: modules.name,
          courseName: courses.name
        })
        .from(quizzes)
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, 
          or(
            eq(chapters.moduleId, modules.id), // For chapter quizzes
            eq(quizzes.moduleId, modules.id)    // For module quizzes
          )
        )
        .leftJoin(courses, 
          or(
            eq(modules.courseId, courses.id),  // For chapter and module quizzes
            eq(quizzes.courseId, courses.id)    // For final exams
          )
        )
        .where(whereCondition);
    } else {
      // Get all quizzes for teacher's courses
      quizList = await db
        .select({
          id: quizzes.id,
          name: quizzes.name,
          description: quizzes.description,
          quizType: quizzes.quizType,
          timeLimit: quizzes.timeLimit,
          minimumScore: quizzes.minimumScore,
          isActive: quizzes.isActive,
          chapterId: quizzes.chapterId,
          moduleId: quizzes.moduleId,
          courseId: quizzes.courseId,
          createdAt: quizzes.createdAt,
          updatedAt: quizzes.updatedAt,
          chapterName: chapters.name,
          moduleName: modules.name,
          courseName: courses.name
        })
        .from(quizzes)
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, 
          or(
            eq(chapters.moduleId, modules.id), // For chapter quizzes
            eq(quizzes.moduleId, modules.id)    // For module quizzes
          )
        )
        .leftJoin(courses, 
          or(
            eq(modules.courseId, courses.id),  // For chapter and module quizzes
            eq(quizzes.courseId, courses.id)    // For final exams
          )
        )
        .where(eq(courses.teacherId, parseInt(teacherId)));
    }

    // Get question count for each quiz
    const quizzesWithQuestionCount = await Promise.all(
      quizList.map(async (quiz) => {
        const questionCount = await db
          .select({ count: questions.id })
          .from(questions)
          .where(eq(questions.quizId, quiz.id));

        return {
          ...quiz,
          questionCount: questionCount.length
        };
      })
    );

    return NextResponse.json({ quizzes: quizzesWithQuestionCount });
  } catch (error) {
    console.error('Error fetching quizzes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/quizzes - Create a new quiz
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      quizType = 'chapter',
      timeLimit,
      minimumScore = 70,
      isActive = true,
      chapterId,
      moduleId,
      courseId,
      teacherId,
      questions: quizQuestions = []
    } = body;

    // Validate required fields based on quiz type
    if (!name || !teacherId) {
      return NextResponse.json(
        { error: 'Name and teacher ID are required' },
        { status: 400 }
      );
    }

    // Validate quiz type
    if (!['chapter', 'module', 'final'].includes(quizType)) {
      return NextResponse.json(
        { error: 'Invalid quiz type. Must be chapter, module, or final' },
        { status: 400 }
      );
    }

    // Validate that only the correct ID is provided for each quiz type
    if (quizType === 'chapter' && (moduleId || courseId)) {
      return NextResponse.json(
        { error: 'Chapter quiz should only have chapterId, not moduleId or courseId' },
        { status: 400 }
      );
    }
    if (quizType === 'module' && (chapterId || courseId)) {
      return NextResponse.json(
        { error: 'Module quiz should only have moduleId, not chapterId or courseId' },
        { status: 400 }
      );
    }
    if (quizType === 'final' && (chapterId || moduleId)) {
      return NextResponse.json(
        { error: 'Final exam should only have courseId, not chapterId or moduleId' },
        { status: 400 }
      );
    }

    let validationResult;
    
    // Validate based on quiz type
    if (quizType === 'chapter') {
      if (!chapterId) {
        return NextResponse.json(
          { error: 'Chapter ID is required for chapter quiz' },
          { status: 400 }
        );
      }
      
      // Verify chapter exists and teacher has access
      validationResult = await db
        .select({
          chapterId: chapters.id,
          moduleId: chapters.moduleId,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(chapters)
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(chapters.id, chapterId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);
        
      if (validationResult.length === 0) {
        return NextResponse.json(
          { error: 'Chapter not found or access denied' },
          { status: 403 }
        );
      }
    } else if (quizType === 'module') {
      if (!moduleId) {
        return NextResponse.json(
          { error: 'Module ID is required for module quiz' },
          { status: 400 }
        );
      }
      
      // Verify module exists and teacher has access
      validationResult = await db
        .select({
          moduleId: modules.id,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(modules)
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(modules.id, moduleId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);
        
      if (validationResult.length === 0) {
        return NextResponse.json(
          { error: 'Module not found or access denied' },
          { status: 403 }
        );
      }
    } else if (quizType === 'final') {
      if (!courseId) {
        return NextResponse.json(
          { error: 'Course ID is required for final exam' },
          { status: 400 }
        );
      }
      
      // Verify course exists and teacher has access
      validationResult = await db
        .select({
          courseId: courses.id,
          teacherId: courses.teacherId
        })
        .from(courses)
        .where(
          and(
            eq(courses.id, courseId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);
        
      if (validationResult.length === 0) {
        return NextResponse.json(
          { error: 'Course not found or access denied' },
          { status: 403 }
        );
      }
    } else {
      return NextResponse.json(
        { error: 'Invalid quiz type. Must be chapter, module, or final' },
        { status: 400 }
      );
    }

    // Additional validation: Check for duplicate quizzes
    if (quizType === 'final') {
      // Only one final exam per course
      const existingFinalExam = await db
        .select({ id: quizzes.id })
        .from(quizzes)
        .where(
          and(
            eq(quizzes.courseId, courseId),
            eq(quizzes.quizType, 'final')
          )
        )
        .limit(1);
        
      if (existingFinalExam.length > 0) {
        return NextResponse.json(
          { error: 'A final exam already exists for this course' },
          { status: 409 }
        );
      }
    } else if (quizType === 'module') {
      // Only one module quiz per module
      const existingModuleQuiz = await db
        .select({ id: quizzes.id })
        .from(quizzes)
        .where(
          and(
            eq(quizzes.moduleId, moduleId),
            eq(quizzes.quizType, 'module')
          )
        )
        .limit(1);
        
      if (existingModuleQuiz.length > 0) {
        return NextResponse.json(
          { error: 'A module quiz already exists for this module' },
          { status: 409 }
        );
      }
    }

    // Prepare quiz data based on type
    const quizData: any = {
      name,
      description,
      quizType,
      timeLimit,
      minimumScore: minimumScore.toString(),
      isActive,
      chapterId: quizType === 'chapter' ? chapterId : null,
      moduleId: quizType === 'module' ? moduleId : null,
      courseId: quizType === 'final' ? courseId : null
    };

    // Create the quiz
    const newQuiz = await db
      .insert(quizzes)
      .values(quizData)
      .returning();

    const quizId = newQuiz[0].id;

    // Create questions if provided
    if (quizQuestions.length > 0) {
      const questionsToInsert = quizQuestions.map((question: any, index: number) => ({
        quizId,
        type: question.type || 'multiple_choice',
        question: JSON.stringify(question.question),
        options: question.options ? JSON.stringify(question.options) : null,
        essayAnswer: question.essayAnswer === '' ? null : question.essayAnswer,
        explanation: question.explanation === '' ? null : question.explanation,
        points: question.points || 1,
        orderIndex: question.orderIndex || index + 1
      }));

      await db.insert(questions).values(questionsToInsert);
    }

    return NextResponse.json(
      { quiz: newQuiz[0], message: 'Quiz created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}