'use client';

import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import {
  CreditCardIcon,
  DollarCircleIcon,
  ShoppingCart01Icon as ShoppingCartIcon,
  CheckmarkCircle01Icon as CheckCircleIcon,
  Cancel01Icon as XCircleIcon,
  LockIcon as LockIcon
} from 'hugeicons-react';
import { Course } from '@/types/lms';

interface PaymentModalProps {
  course: Course;
  isOpen: boolean;
  onClose: () => void;
  onPaymentSuccess: () => void;
}

const PaymentModal: React.FC<PaymentModalProps> = ({
  course,
  isOpen,
  onClose,
  onPaymentSuccess
}) => {
  const [paymentMethod, setPaymentMethod] = useState<'card' | 'paypal' | 'bank'>('card');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentStep, setPaymentStep] = useState<'details' | 'processing' | 'success' | 'error'>('details');
  const [formData, setFormData] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    email: '',
    billingAddress: ''
  });

  const formatPrice = (price: number, currency: string = 'IDR') => {
    if (currency === 'IDR') {
      return 'Rp' + new Intl.NumberFormat('id-ID').format(price);
    }
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: currency
    }).format(price);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = () => {
    if (paymentMethod === 'card') {
      return formData.cardNumber && 
             formData.expiryDate && 
             formData.cvv && 
             formData.cardholderName &&
             formData.email;
    }
    return formData.email;
  };

  const processPayment = async () => {
    if (!validateForm()) {
      return;
    }

    setIsProcessing(true);
    setPaymentStep('processing');

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // For demo purposes, randomly succeed or fail
      if (Math.random() > 0.1) { // 90% success rate
        setPaymentStep('success');
        setTimeout(() => {
          onPaymentSuccess();
          onClose();
          resetModal();
        }, 2000);
      } else {
        setPaymentStep('error');
      }
    } catch (error) {
      setPaymentStep('error');
    } finally {
      setIsProcessing(false);
    }
  };

  const resetModal = () => {
    setPaymentStep('details');
    setFormData({
      cardNumber: '',
      expiryDate: '',
      cvv: '',
      cardholderName: '',
      email: '',
      billingAddress: ''
    });
    setIsProcessing(false);
  };

  const handleClose = () => {
    if (!isProcessing && paymentStep !== 'success') {
      onClose();
      resetModal();
    }
  };

  const renderPaymentDetails = () => (
    <div className="space-y-6">
      {/* Course Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-semibold mb-2">Ringkasan Kursus</h4>
        <div className="flex justify-between items-start mb-2">
          <div>
            <p className="font-medium">{course.name}</p>
            <p className="text-sm text-gray-600">by {course.instructor}</p>
          </div>
          <div className="text-right">
            <p className="text-xl font-bold text-green-600">
              {course.price ? formatPrice(course.price, course.currency) : 'Free'}
            </p>
          </div>
        </div>
        <div className="flex gap-2 text-sm text-gray-600">
          <Badge variant="outline">{course.modules.length} modul</Badge>
          {course.certificate.isEligible && (
            <Badge variant="outline">Sertifikat disertakan</Badge>
          )}
        </div>
      </div>

      {/* Payment Method Selection */}
      <div>
        <Label className="text-base font-semibold mb-3 block">Metode Pembayaran</Label>
        <div className="grid grid-cols-3 gap-2">
          <Button
            variant={paymentMethod === 'card' ? 'default' : 'outline'}
            onClick={() => setPaymentMethod('card')}
            className="h-16 flex-col gap-1"
          >
            <CreditCardIcon className="h-5 w-5" />
            <span className="text-xs">Kartu Kredit</span>
          </Button>
          <Button
            variant={paymentMethod === 'paypal' ? 'default' : 'outline'}
            onClick={() => setPaymentMethod('paypal')}
            className="h-16 flex-col gap-1"
          >
            <DollarCircleIcon className="h-5 w-5" />
            <span className="text-xs">PayPal</span>
          </Button>
          <Button
            variant={paymentMethod === 'bank' ? 'default' : 'outline'}
            onClick={() => setPaymentMethod('bank')}
            className="h-16 flex-col gap-1"
          >
            <ShoppingCartIcon className="h-5 w-5" />
            <span className="text-xs">Transfer Bank</span>
          </Button>
        </div>
      </div>

      {/* Payment Form */}
      {paymentMethod === 'card' && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="email">Alamat Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
            />
          </div>
          
          <div>
            <Label htmlFor="cardholderName">Nama Pemegang Kartu</Label>
            <Input
              id="cardholderName"
              placeholder="Nama lengkap pada kartu"
              value={formData.cardholderName}
              onChange={(e) => handleInputChange('cardholderName', e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="cardNumber">Nomor Kartu</Label>
            <Input
              id="cardNumber"
              placeholder="1234 5678 9012 3456"
              value={formData.cardNumber}
              onChange={(e) => handleInputChange('cardNumber', e.target.value)}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="expiryDate">Tanggal Kedaluwarsa</Label>
              <Input
                id="expiryDate"
                placeholder="BB/TT"
                value={formData.expiryDate}
                onChange={(e) => handleInputChange('expiryDate', e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="cvv">CVV</Label>
              <Input
                id="cvv"
                placeholder="123"
                value={formData.cvv}
                onChange={(e) => handleInputChange('cvv', e.target.value)}
              />
            </div>
          </div>
        </div>
      )}

      {paymentMethod === 'paypal' && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="email">Alamat Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
            />
          </div>
          <div className="text-center py-4">
            <p className="text-sm text-gray-600">
              Anda akan dialihkan ke PayPal untuk menyelesaikan pembayaran
            </p>
          </div>
        </div>
      )}

      {paymentMethod === 'bank' && (
        <div className="space-y-4">
          <div>
            <Label htmlFor="email">Alamat Email</Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
            />
          </div>
          <div className="text-center py-4">
            <p className="text-sm text-gray-600">
              Instruksi transfer bank akan dikirim ke email Anda
            </p>
          </div>
        </div>
      )}

      {/* Security Notice */}
      <div className="flex items-center gap-2 text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
        <LockIcon className="h-4 w-4" />
        <span>Informasi pembayaran Anda dienkripsi dan aman</span>
      </div>

      <Separator />

      {/* Action Buttons */}
      <div className="flex gap-3">
        <Button
          variant="outline"
          onClick={handleClose}
          className="flex-1"
          disabled={isProcessing}
        >
          Batal
        </Button>
        <Button
          onClick={processPayment}
          className="flex-1 bg-green-600 hover:bg-green-700"
          disabled={!validateForm() || isProcessing}
        >
          <ShoppingCartIcon className="mr-2 h-4 w-4" />
          Selesaikan Pembelian
        </Button>
      </div>
    </div>
  );

  const renderProcessing = () => (
    <div className="text-center py-8">
      <div className="animate-spin h-12 w-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4" />
      <h4 className="text-lg font-semibold mb-2">Memproses Pembayaran</h4>
      <p className="text-gray-600">Harap tunggu sementara kami memproses pembayaran Anda...</p>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center py-8">
      <CheckCircleIcon className="h-16 w-16 text-green-600 mx-auto mb-4" />
      <h4 className="text-xl font-semibold mb-2">Pembayaran Berhasil!</h4>
      <p className="text-gray-600 mb-4">
        Anda telah berhasil terdaftar di {course.name}
      </p>
      <p className="text-sm text-gray-500">
        Mengalihkan ke konten kursus...
      </p>
    </div>
  );

  const renderError = () => (
    <div className="text-center py-8">
      <XCircleIcon className="h-16 w-16 text-red-600 mx-auto mb-4" />
      <h4 className="text-xl font-semibold mb-2">Pembayaran Gagal</h4>
      <p className="text-gray-600 mb-6">
        Terjadi masalah saat memproses pembayaran Anda. Silakan coba lagi.
      </p>
      <div className="flex gap-3 justify-center">
        <Button variant="outline" onClick={handleClose}>
          Batal
        </Button>
        <Button onClick={() => setPaymentStep('details')}>
          Coba Lagi
        </Button>
      </div>
    </div>
  );

  const getDialogContent = () => {
    switch (paymentStep) {
      case 'processing':
        return renderProcessing();
      case 'success':
        return renderSuccess();
      case 'error':
        return renderError();
      default:
        return renderPaymentDetails();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <ShoppingCartIcon className="h-5 w-5" />
            {paymentStep === 'success' ? 'Pembelian Selesai' : 'Beli Kursus'}
          </DialogTitle>
          {paymentStep === 'details' && (
            <DialogDescription>
              Selesaikan pembelian Anda untuk mendapatkan akses instan ke kursus
            </DialogDescription>
          )}
        </DialogHeader>
        {getDialogContent()}
      </DialogContent>
    </Dialog>
  );
};

export default PaymentModal;