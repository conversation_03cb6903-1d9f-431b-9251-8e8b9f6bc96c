import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Calendar, ClipboardList, BookOpen } from 'lucide-react';
import { CourseData, AdmissionsData } from '../course-creation-wizard';

interface AdmissionsStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function AdmissionsStep({ data, onUpdate }: AdmissionsStepProps) {
  const admissions = data.admissions || { requirements: [], applicationDeadline: '', prerequisites: [] };

  const handleUpdate = (field: keyof AdmissionsData, value: string | string[]) => {
    onUpdate({
      admissions: {
        ...admissions,
        [field]: value,
      },
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Informasi Pendaftaran</CardTitle>
        <CardDescription>Detail terkait persyaratan pendaftaran dan prasyarat kursus.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2">
          <ClipboardList className="h-5 w-5 text-gray-500" />
          <Label htmlFor="requirements">Persyaratan (pisahkan dengan koma)</Label>
        </div>
        <Textarea
          id="requirements"
          value={admissions.requirements.join(', ')}
          onChange={(e) => handleUpdate('requirements', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Gelar Sarjana Arsitektur, Pengalaman kerja minimal 2 tahun"
        />

        <div className="flex items-center space-x-2">
          <Calendar className="h-5 w-5 text-gray-500" />
          <Label htmlFor="applicationDeadline">Batas Waktu Pendaftaran</Label>
        </div>
        <Input
          id="applicationDeadline"
          type="text" // Could be a date picker in a real app
          value={admissions.applicationDeadline}
          onChange={(e) => handleUpdate('applicationDeadline', e.target.value)}
          placeholder="Contoh: 2024-12-31"
        />

        <div className="flex items-center space-x-2">
          <BookOpen className="h-5 w-5 text-gray-500" />
          <Label htmlFor="prerequisites">Prasyarat (pisahkan dengan koma)</Label>
        </div>
        <Textarea
          id="prerequisites"
          value={admissions.prerequisites.join(', ')}
          onChange={(e) => handleUpdate('prerequisites', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Penguasaan AutoCAD, Pemahaman standar SNI"
        />
      </CardContent>
    </Card>
  );
}