'use client';

import React, { useState, useEffect } from 'react';
import { Course } from '@/types/lms';
import CoursePreviewCard from '@/components/lms/course-preview-card';
import { architectureCourse } from '@/constants/shared-course-data';

interface CourseGridProps {
  courses?: Course[];
  onEnroll: (courseId: string) => void;
  onPreview: (courseId: string) => void;
  enrolledCourses?: string[];
}

export default function CourseGrid({ 
  courses: propCourses, 
  onEnroll, 
  onPreview,
  enrolledCourses = []
}: CourseGridProps) {
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    console.log('CourseGrid received propCourses:', propCourses);
    if (propCourses && propCourses.length > 0) {
      setCourses(propCourses);
    } else {
      // If no courses provided, use the sample architecture course
      setCourses([architectureCourse]);
    }
  }, [propCourses]);

  const fetchCourses = async () => {
    try {
      setLoading(true);
      // This would be your actual API call
      // const response = await fetch('/api/courses');
      // if (!response.ok) {
      //   throw new Error('Failed to fetch courses');
      // }
      // const data = await response.json();
      // setCourses(data);
      
      // For now, using sample data
      setCourses([architectureCourse]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <section id="courses" className="min-h-screen w-full flex flex-col">
        <div className="flex-1 flex flex-col justify-center items-center px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Available Courses
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-medium">
              Discover professional certification programs designed to advance your career.
            </p>
          </div>
          
          <div className="flex justify-center items-center h-32">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="courses" className="min-h-screen w-full flex flex-col">
        <div className="flex-1 flex flex-col justify-center items-center px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Available Courses
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-medium">
              Discover professional certification programs designed to advance your career.
            </p>
          </div>
          
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            Error: {error}
          </div>
        </div>
      </section>
    );
  }

  if (courses.length === 0) {
    return (
      <section id="courses" className="min-h-screen w-full flex flex-col">
        <div className="flex-1 flex flex-col justify-center items-center px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
              Available Courses
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto font-medium">
              Discover professional certification programs designed to advance your career.
            </p>
          </div>
          
          <div className="text-center">
            <div className="text-6xl mb-4">🎓</div>
            <p className="text-xl text-gray-600">
              No courses available at the moment. Check back soon for new programs!
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="courses" className="py-16 lg:py-24 bg-gray-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12 lg:mb-16">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6">
              Kursus Tersedia
            </h2>
            <p className="text-xl md:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Temukan program sertifikasi profesional yang dirancang untuk memajukan karir Anda di bidang arsitektur dan lainnya.
            </p>
          </div>
          
          {/* Course Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            <div className="bg-white rounded-xl p-6 shadow-lg text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{courses.length}</div>
              <div className="text-gray-600">Kursus Tersedia</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-lg text-center">
              <div className="text-3xl font-bold text-green-600 mb-2">
                {courses.reduce((total, course) => total + course.modules.length, 0)}
              </div>
              <div className="text-gray-600">Total Modul</div>
            </div>
            <div className="bg-white rounded-xl p-6 shadow-lg text-center">
              <div className="text-3xl font-bold text-purple-600 mb-2">100%</div>
              <div className="text-gray-600">Pembelajaran Online</div>
            </div>
          </div>

          {/* Courses Carousel */}
          <div className="relative">
            <div className="overflow-x-auto scrollbar-hide">
              <div className="flex space-x-6 pb-4">
                {courses.map((course) => (
                  <div key={course.id} className="flex-none w-80 md:w-96">
                    <CoursePreviewCard 
                      course={course} 
                      onEnroll={() => onEnroll(course.id)}
                      onPurchase={() => onPreview(course.id)}
                      onClick={() => onPreview(course.id)}
                    />
                  </div>
                ))}
              </div>
            </div>
            
            {/* Scroll indicators */}
            <div className="flex justify-center mt-6 space-x-2">
              {courses.map((_, index) => (
                <div
                  key={index}
                  className="w-2 h-2 rounded-full bg-gray-300"
                />
              ))}
            </div>
          </div>

          {/* Call to Action */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-br from-gray-900 via-gray-800 to-black rounded-2xl p-8 lg:p-12 text-white">
              <h3 className="text-2xl lg:text-3xl font-bold mb-4">
                Siap Memulai Perjalanan Profesional Anda?
              </h3>
              <p className="text-lg lg:text-xl text-gray-200 mb-6 max-w-2xl mx-auto">
                Bergabunglah dengan ribuan profesional yang telah memajukan karir mereka dengan program sertifikasi kami.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button 
                  onClick={() => onEnroll(courses[0]?.id || '')}
                  className="bg-white text-gray-900 font-bold py-3 px-8 rounded-lg hover:bg-gray-100 transition-colors duration-200"
                >
                  Mulai Kursus Pertama
                </button>
                <button 
                  onClick={() => onPreview(courses[0]?.id || '')}
                  className="border-2 border-white text-white font-bold py-3 px-8 rounded-lg hover:bg-white hover:text-gray-900 transition-colors duration-200"
                >
                  Jelajahi Semua Kursus
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </section>
  );
}