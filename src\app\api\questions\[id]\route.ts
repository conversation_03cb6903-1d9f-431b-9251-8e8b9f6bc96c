import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { questions, quizzes, chapters, modules, courses } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/questions/[id] - Get a specific question
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const questionId = parseInt(id);
    
    if (isNaN(questionId)) {
      return NextResponse.json({ error: 'Invalid question ID' }, { status: 400 });
    }

    // Get question
    const questionData = await db
      .select()
      .from(questions)
      .where(eq(questions.id, questionId))
      .limit(1);

    if (questionData.length === 0) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 });
    }

    const question = questionData[0];

    // Parse options
    const questionWithParsedData = {
      ...question,
      question: question.question,
      options: question.options,
      essayAnswer: question.essayAnswer, // Ensure this maps correctly from DB
      explanation: question.explanation, // Ensure this maps correctly from DB
    };

    return NextResponse.json({ question: questionWithParsedData });
  } catch (error) {
    console.error('Error fetching question:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/questions/[id] - Update a question
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const questionId = parseInt(id);
    
    if (isNaN(questionId)) {
      return NextResponse.json({ error: 'Invalid question ID' }, { status: 400 });
    }

    const body = await request.json();
    const {
      type,
      question,
      options,
      essayAnswer, // Renamed from correctAnswer
      explanation, // New field
      points,
      orderIndex,
      teacherId
    } = body;

    // Check if question exists
    const existingQuestion = await db
      .select()
      .from(questions)
      .where(eq(questions.id, questionId))
      .limit(1);

    if (existingQuestion.length === 0) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 });
    }

    // Verify teacher has permission to update this question
    if (teacherId) {
      const questionWithCourse = await db
        .select({
          questionId: questions.id,
          quizId: questions.quizId,
          teacherId: courses.teacherId
        })
        .from(questions)
        .leftJoin(quizzes, eq(questions.quizId, quizzes.id))
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(questions.id, questionId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);

      if (questionWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Not authorized to update this question' },
          { status: 403 }
        );
      }
    }

    // Update the question
    const updatedQuestion = await db
      .update(questions)
      .set({
        ...(type && { type }),
        ...(question && { question: JSON.stringify(question) }),
        ...(options && { options: JSON.stringify(options) }),
        ...(essayAnswer !== undefined && { essayAnswer: essayAnswer === '' ? null : essayAnswer }),
        ...(explanation !== undefined && { explanation: explanation === '' ? null : explanation }),
        ...(points !== undefined && { points: points.toString() }),
        ...(orderIndex !== undefined && { orderIndex }),
        updatedAt: new Date()
      })
      .where(eq(questions.id, questionId))
      .returning();

    // Parse options for response
    const questionWithParsedData = {
      ...updatedQuestion[0],
      question: updatedQuestion[0].question,
      options: updatedQuestion[0].options,
      essayAnswer: updatedQuestion[0].essayAnswer,
      explanation: updatedQuestion[0].explanation,
    };

    return NextResponse.json({
      question: questionWithParsedData,
      message: 'Question updated successfully'
    });
  } catch (error) {
    console.error('Error updating question:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/questions/[id] - Delete a question
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const questionId = parseInt(id);
    
    if (isNaN(questionId)) {
      return NextResponse.json({ error: 'Invalid question ID' }, { status: 400 });
    }

    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');

    // Check if question exists
    const existingQuestion = await db
      .select()
      .from(questions)
      .where(eq(questions.id, questionId))
      .limit(1);

    if (existingQuestion.length === 0) {
      return NextResponse.json({ error: 'Question not found' }, { status: 404 });
    }

    // Verify teacher has permission to delete this question
    if (teacherId) {
      const questionWithCourse = await db
        .select({
          questionId: questions.id,
          quizId: questions.quizId,
          teacherId: courses.teacherId
        })
        .from(questions)
        .leftJoin(quizzes, eq(questions.quizId, quizzes.id))
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(questions.id, questionId),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (questionWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Not authorized to delete this question' },
          { status: 403 }
        );
      }
    }

    // Delete the question
    await db.delete(questions).where(eq(questions.id, questionId));

    return NextResponse.json({ message: 'Question deleted successfully' });
  } catch (error) {
    console.error('Error deleting question:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}