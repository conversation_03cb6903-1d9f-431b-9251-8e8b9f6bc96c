'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Upload, X } from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';
import Image from 'next/image';

export default function NewClassPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });
  const [coverImage, setCoverImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isUploading, setIsUploading] = useState(false);

  const uploadImageToBlob = async (file: File): Promise<string | null> => {
    try {
      setIsUploading(true);
      
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('Failed to upload image');
      }
      
      const data = await response.json();
      return data.url;
    } catch (error) {
      console.error('Error uploading image:', error);
      toast.error('Failed to upload image');
      return null;
    } finally {
      setIsUploading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to create classes');
        return;
      }

      if (!user.institutionId) {
        toast.error('You must be assigned to an institution to create classes');
        return;
      }

      if (!formData.name.trim()) {
        toast.error('Class name is required');
        return;
      }

      let coverPictureUrl = null;
      
      // Upload image if selected
      if (coverImage) {
        coverPictureUrl = await uploadImageToBlob(coverImage);
        if (!coverPictureUrl) {
          return; // Upload failed, stop submission
        }
      }

      const response = await fetch('/api/classes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim(),
          teacherId: user.id,
          institutionId: user.institutionId,
          coverPicture: coverPictureUrl
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Class created successfully!');
        router.push('/dashboard/teacher/classes');
      } else {
        toast.error(data.error || 'Failed to create class');
      }
    } catch (error) {
      console.error('Error creating class:', error);
      toast.error('Failed to create class');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file');
        return;
      }
      
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size must be less than 5MB');
        return;
      }
      
      setCoverImage(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const removeImage = () => {
    setCoverImage(null);
    setImagePreview(null);
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/teacher/classes'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Create New Class
          </h1>
          <p className='text-muted-foreground'>
            Create a new class to organize your students
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Class Details</CardTitle>
          <CardDescription>
            Enter the basic information for the new class
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Class Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder='e.g., Mathematics Grade 10A'
                  required
                />
                <p className='text-muted-foreground text-sm'>
                  Choose a descriptive name that includes subject and grade
                  level
                </p>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='description'>Description</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange('description', e.target.value)
                  }
                  placeholder='Brief description of the class and its objectives'
                  rows={4}
                />
                <p className='text-muted-foreground text-sm'>
                  Provide a brief description of what this class covers
                </p>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='coverImage'>Cover Image</Label>
                <div className='space-y-4'>
                  {imagePreview ? (
                    <div className='relative w-full max-w-md'>
                      <Image
                        src={imagePreview}
                        alt='Cover preview'
                        width={400}
                        height={200}
                        className='rounded-lg object-cover w-full h-48'
                      />
                      <Button
                        type='button'
                        variant='destructive'
                        size='sm'
                        className='absolute top-2 right-2'
                        onClick={removeImage}
                      >
                        <X className='h-4 w-4' />
                      </Button>
                    </div>
                  ) : (
                    <div className='border-2 border-dashed border-gray-300 rounded-lg p-6 text-center'>
                      <Upload className='mx-auto h-12 w-12 text-gray-400' />
                      <div className='mt-4'>
                        <Label
                          htmlFor='coverImage'
                          className='cursor-pointer inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700'
                        >
                          Choose Image
                        </Label>
                        <Input
                          id='coverImage'
                          type='file'
                          accept='image/*'
                          onChange={handleImageChange}
                          className='hidden'
                        />
                      </div>
                      <p className='mt-2 text-sm text-gray-500'>
                        PNG, JPG, GIF up to 5MB
                      </p>
                    </div>
                  )}
                </div>
                <p className='text-muted-foreground text-sm'>
                  Upload a cover image for your class (optional)
                </p>
              </div>
            </div>

            <div className='flex justify-end space-x-4'>
              <Link href='/dashboard/teacher/classes'>
                <Button variant='outline' type='button'>
                  Cancel
                </Button>
              </Link>
              <Button type='submit' disabled={isLoading || isUploading}>
                <Save className='mr-2 h-4 w-4' />
                {isUploading ? 'Uploading Image...' : isLoading ? 'Creating...' : 'Create Class'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Next Steps Card */}
      <Card>
        <CardHeader>
          <CardTitle>Next Steps</CardTitle>
          <CardDescription>After creating your class, you can:</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className='space-y-2 text-sm'>
            <li className='flex items-center space-x-2'>
              <span className='h-2 w-2 rounded-full bg-blue-500'></span>
              <span>Add students to your class</span>
            </li>
            <li className='flex items-center space-x-2'>
              <span className='h-2 w-2 rounded-full bg-blue-500'></span>
              <span>Create or assign courses to the class</span>
            </li>
            <li className='flex items-center space-x-2'>
              <span className='h-2 w-2 rounded-full bg-blue-500'></span>
              <span>Generate course codes for student enrollment</span>
            </li>
            <li className='flex items-center space-x-2'>
              <span className='h-2 w-2 rounded-full bg-blue-500'></span>
              <span>Track student progress and performance</span>
            </li>
          </ul>
        </CardContent>
      </Card>
    </div>
  );
}
