'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  User,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Building2,
  Loader2,
  Filter
} from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  institution_id: number | null;
  institution_name: string | null;
  created_at: string;
  updated_at: string;
}

interface Institution {
  id: number;
  name: string;
}

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [institutionFilter, setInstitutionFilter] = useState('all');
  const [users, setUsers] = useState<User[]>([]);
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<number | null>(null);
  const { toast } = useToast();
  const isInitialMount = useRef(true);

  // Fetch institutions for filter dropdown
  const fetchInstitutions = useCallback(async () => {
    try {
      const response = await fetch('/api/institutions');
      const data = await response.json();
      if (data.success) {
        setInstitutions(data.data.institutions);
      }
    } catch (error) {
      console.error('Error fetching institutions:', error);
    }
  }, []);

  // Fetch users from API
  const fetchUsers = useCallback(
    async (search: string = '', role: string = 'all', institution: string = 'all') => {
      try {
        setLoading(true);
        const params = new URLSearchParams();
        if (search) params.append('search', search);
        if (role !== 'all') params.append('role', role);
        if (institution !== 'all') params.append('institution_id', institution);
        
        const response = await fetch(`/api/users?${params.toString()}`);
        const data = await response.json();

        if (data.success) {
          setUsers(data.data.users);
        } else {
          toast({
            title: 'Error',
            description: data.error || 'Failed to fetch users',
            variant: 'destructive'
          });
        }
      } catch (error) {
        console.error('Error fetching users:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch users',
          variant: 'destructive'
        });
      } finally {
        setLoading(false);
      }
    },
    []
  );

  // Delete user
  const handleDelete = async (id: number) => {
    if (
      !confirm(
        'Are you sure you want to delete this user? This action cannot be undone.'
      )
    ) {
      return;
    }

    try {
      setDeleting(id);
      const response = await fetch(`/api/users/${id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'User deleted successfully'
        });
        // Refresh the list with current filters
        fetchUsers(searchTerm, roleFilter, institutionFilter);
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to delete user',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error deleting user:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete user',
        variant: 'destructive'
      });
    } finally {
      setDeleting(null);
    }
  };

  // Initial fetch on component mount
  useEffect(() => {
    fetchInstitutions();
    fetchUsers();
  }, []);

  // Debounced search and filter effect
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    const timeoutId = setTimeout(() => {
      fetchUsers(searchTerm, roleFilter, institutionFilter);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm, roleFilter, institutionFilter, fetchUsers]);

  const getRoleBadge = (role: string) => {
    const variant =
      role === 'super_admin'
        ? 'default'
        : role === 'teacher'
          ? 'secondary'
          : 'outline';
    return <Badge variant={variant}>{role.replace('_', ' ')}</Badge>;
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Users</h1>
          <p className='text-muted-foreground'>
            Manage all users on the platform
          </p>
        </div>
        <Link href='/dashboard/admin/users/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Add User
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Users</CardTitle>
          <CardDescription>
            View and manage all registered users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search users by name or email...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className='w-[150px]'>
                <Filter className='mr-2 h-4 w-4' />
                <SelectValue placeholder='Role' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Roles</SelectItem>
                <SelectItem value='student'>Student</SelectItem>
                <SelectItem value='teacher'>Teacher</SelectItem>
                <SelectItem value='super_admin'>Super Admin</SelectItem>
              </SelectContent>
            </Select>
            <Select value={institutionFilter} onValueChange={setInstitutionFilter}>
              <SelectTrigger className='w-[200px]'>
                <Building2 className='mr-2 h-4 w-4' />
                <SelectValue placeholder='Institution' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='all'>All Institutions</SelectItem>
                <SelectItem value='null'>No Institution</SelectItem>
                {institutions.map((institution) => (
                  <SelectItem key={institution.id} value={institution.id.toString()}>
                    {institution.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>User</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Institution</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={5} className='py-8 text-center'>
                      <Loader2 className='mx-auto h-6 w-6 animate-spin' />
                      <p className='text-muted-foreground mt-2 text-sm'>
                        Loading users...
                      </p>
                    </TableCell>
                  </TableRow>
                ) : users.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className='py-8 text-center'>
                      <User className='text-muted-foreground mx-auto mb-4 h-12 w-12' />
                      <h3 className='mb-2 text-sm font-semibold'>
                        No users found
                      </h3>
                      <p className='text-muted-foreground mb-4 text-sm'>
                        {searchTerm || roleFilter !== 'all' || institutionFilter !== 'all'
                          ? 'Try adjusting your search or filters.'
                          : 'Get started by adding a new user.'}
                      </p>
                      {!searchTerm && roleFilter === 'all' && institutionFilter === 'all' && (
                        <Link href='/dashboard/admin/users/new'>
                          <Button>
                            <Plus className='mr-2 h-4 w-4' />
                            Add User
                          </Button>
                        </Link>
                      )}
                    </TableCell>
                  </TableRow>
                ) : (
                  users.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className='flex items-center space-x-2'>
                          <User className='text-muted-foreground h-4 w-4' />
                          <div>
                            <p className='font-medium'>{user.name}</p>
                            <p className='text-muted-foreground text-sm'>
                              {user.email}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getRoleBadge(user.role)}
                      </TableCell>
                      <TableCell>
                        {user.institution_name ? (
                          <div className='flex items-center space-x-1'>
                            <Building2 className='text-muted-foreground h-3 w-3' />
                            <span className='text-sm'>{user.institution_name}</span>
                          </div>
                        ) : (
                          <span className='text-muted-foreground text-sm'>No institution</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <span className='text-sm'>
                          {new Date(user.created_at).toLocaleDateString()}
                        </span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant='ghost'
                              className='h-8 w-8 p-0'
                              disabled={deleting === user.id}
                            >
                              {deleting === user.id ? (
                                <Loader2 className='h-4 w-4 animate-spin' />
                              ) : (
                                <MoreHorizontal className='h-4 w-4' />
                              )}
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/admin/users/${user.id}`}>
                                <Edit className='mr-2 h-4 w-4' />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              className='text-red-600'
                              onClick={() => handleDelete(user.id)}
                              disabled={deleting === user.id || user.role === 'super_admin'}
                            >
                              <Trash2 className='mr-2 h-4 w-4' />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}