'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save, Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';

interface Institution {
  id: number;
  name: string;
}

export default function NewUserPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: 'student',
    institutionId: 'none'
  });

  // Fetch institutions for dropdown
  useEffect(() => {
    const fetchInstitutions = async () => {
      try {
        const response = await fetch('/api/institutions');
        const data = await response.json();
        if (data.success) {
          setInstitutions(data.data.institutions);
        }
      } catch (error) {
        console.error('Error fetching institutions:', error);
      }
    };

    fetchInstitutions();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const submitData = {
        ...formData,
        institutionId: formData.institutionId && formData.institutionId !== 'none' ? parseInt(formData.institutionId) : null
      };

      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'User created successfully'
        });
        router.push('/dashboard/admin/users');
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to create user',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error creating user:', error);
      toast({
        title: 'Error',
        description: 'Failed to create user',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    handleInputChange('password', password);
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/admin/users'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Add New User
          </h1>
          <p className='text-muted-foreground'>
            Create a new user account on the platform
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Details</CardTitle>
          <CardDescription>
            Enter the basic information for the new user
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Full Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder='Enter full name'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='email'>Email Address</Label>
                <Input
                  id='email'
                  type='email'
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder='Enter email address'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='password'>Password</Label>
                <div className='relative'>
                  <Input
                    id='password'
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder='Enter password'
                    required
                    className='pr-20'
                  />
                  <div className='absolute inset-y-0 right-0 flex items-center space-x-1 pr-2'>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => setShowPassword(!showPassword)}
                      className='h-7 w-7 p-0'
                    >
                      {showPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={generatePassword}
                  className='mt-2'
                >
                  Generate Password
                </Button>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='role'>User Role</Label>
                <Select
                  value={formData.role}
                  onValueChange={(value) => handleInputChange('role', value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select user role' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='student'>Student</SelectItem>
                    <SelectItem value='teacher'>Teacher</SelectItem>
                    <SelectItem value='super_admin'>Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2 md:col-span-2'>
                <Label htmlFor='institution'>Institution (Optional)</Label>
                <Select
                  value={formData.institutionId}
                  onValueChange={(value) => handleInputChange('institutionId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select institution (optional)' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='none'>No Institution</SelectItem>
                    {institutions.map((institution) => (
                      <SelectItem key={institution.id} value={institution.id.toString()}>
                        {institution.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Role Information */}
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>Role Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='space-y-2 text-sm'>
                  {formData.role === 'student' && (
                    <div>
                      <h4 className='font-semibold'>Student Role:</h4>
                      <ul className='mt-1 space-y-1 text-muted-foreground'>
                        <li>• Can enroll in courses and view course materials</li>
                        <li>• Can take quizzes and submit assignments</li>
                        <li>• Can view their progress and grades</li>
                        <li>• Limited to their assigned institution (if any)</li>
                      </ul>
                    </div>
                  )}
                  {formData.role === 'teacher' && (
                    <div>
                      <h4 className='font-semibold'>Teacher Role:</h4>
                      <ul className='mt-1 space-y-1 text-muted-foreground'>
                        <li>• Can create and manage courses</li>
                        <li>• Can create quizzes and assignments</li>
                        <li>• Can view and grade student submissions</li>
                        <li>• Can manage students within their institution</li>
                      </ul>
                    </div>
                  )}
                  {formData.role === 'super_admin' && (
                    <div>
                      <h4 className='font-semibold'>Super Admin Role:</h4>
                      <ul className='mt-1 space-y-1 text-muted-foreground'>
                        <li>• Full access to all platform features</li>
                        <li>• Can manage all institutions and users</li>
                        <li>• Can view billing and subscription information</li>
                        <li>• Can access system-wide analytics and reports</li>
                      </ul>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <div className='flex justify-end space-x-4'>
              <Link href='/dashboard/admin/users'>
                <Button variant='outline' type='button'>
                  Cancel
                </Button>
              </Link>
              <Button type='submit' disabled={isLoading}>
                <Save className='mr-2 h-4 w-4' />
                {isLoading ? 'Creating...' : 'Create User'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}