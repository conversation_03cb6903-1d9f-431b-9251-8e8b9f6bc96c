# Course Management Technical Architecture - Terang LMS

## 1. Architecture Design

```mermaid
graph TD
    A[Teacher Browser] --> B[React Frontend - Course Management]
    C[Student Browser] --> D[React Frontend - Learning Interface]
    B --> E[Next.js API Routes]
    D --> E
    E --> F[Neon PostgreSQL Database]
    E --> G[File Storage - Course Materials]
    
    subgraph "Frontend Layer"
        B
        D
    end
    
    subgraph "API Layer"
        E
    end
    
    subgraph "Data Layer"
        F
        G
    end
```

## 2. Technology Description

- Frontend: React@18 + Next.js@15 + TypeScript + Tailwind CSS + Shadcn UI
- Backend: Next.js API Routes + Drizzle ORM
- Database: Neon PostgreSQL
- File Storage: Vercel Blob Storage (untuk course materials)
- State Management: React Context + useState/useEffect
- Form Handling: React Hook Form + Zod validation

## 3. Route Definitions

| Route | Purpose |
|-------|----------|
| /dashboard/teacher/courses/new | Course creation wizard dengan module structure |
| /dashboard/teacher/courses/[id]/structure | Course structure management interface |
| /dashboard/teacher/courses/[id]/modules/[moduleId] | Module management dan chapter creation |
| /dashboard/teacher/courses/[id]/chapters/[chapterId]/edit | Chapter content editor dengan quiz builder |
| /dashboard/teacher/courses/[id]/quizzes | Quiz management dashboard |
| /dashboard/student/courses/[id] | Student course learning interface |
| /dashboard/student/courses/[id]/modules/[moduleId]/chapters/[chapterId] | Chapter content view untuk student |
| /dashboard/student/courses/[id]/quiz/[quizId] | Quiz taking interface |

## 4. API Definitions

### 4.1 Course Management APIs

**Create Course dengan Module Structure**
```
POST /api/courses
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| name | string | true | Nama course |
| description | string | false | Deskripsi course |
| type | enum | true | 'self_paced' atau 'verified' |
| startDate | string | false | Tanggal mulai course (ISO format) |
| endDate | string | false | Tanggal selesai course (ISO format) |
| courseCode | string | false | Kode course (auto-generate jika kosong) |
| coverPicture | string | false | URL cover image |
| modules | array | false | Array module structure |

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| success | boolean | Status response |
| courseId | number | ID course yang dibuat |
| message | string | Pesan response |

Example Request:
```json
{
  "name": "Introduction to Mathematics",
  "description": "Basic mathematics course",
  "type": "self_paced",
  "startDate": "2024-01-15T00:00:00Z",
  "endDate": "2024-06-15T00:00:00Z",
  "modules": [
    {
      "name": "Basic Algebra",
      "description": "Introduction to algebraic concepts",
      "orderIndex": 1,
      "chapters": [
        {
          "name": "Variables and Expressions",
          "orderIndex": 1
        }
      ]
    }
  ]
}
```

**Get Course dengan Full Structure**
```
GET /api/courses/[id]/structure
```

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| course | object | Course information |
| modules | array | Array modules dengan chapters dan quizzes |
| statistics | object | Course statistics (enrollment, completion) |

### 4.2 Module Management APIs

**Create Module**
```
POST /api/modules
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| name | string | true | Nama module |
| description | string | false | Deskripsi module |
| courseId | number | true | ID course |
| orderIndex | number | true | Urutan module dalam course |
| startDate | string | false | Tanggal mulai module |
| endDate | string | false | Tanggal selesai module |

**Update Module Order**
```
PUT /api/modules/reorder
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| courseId | number | true | ID course |
| moduleOrders | array | true | Array {moduleId, orderIndex} |

### 4.3 Chapter Management APIs

**Create Chapter dengan Content**
```
POST /api/chapters
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| name | string | true | Nama chapter |
| content | string | false | Konten markdown |
| moduleId | number | true | ID module |
| orderIndex | number | true | Urutan chapter dalam module |

**Update Chapter Content**
```
PUT /api/chapters/[id]/content
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| content | string | true | Konten markdown yang diupdate |
| autoSave | boolean | false | Flag untuk auto-save |

### 4.4 Quiz Management APIs

**Create Quiz dengan Questions**
```
POST /api/quizzes
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| name | string | true | Nama quiz |
| description | string | false | Deskripsi quiz |
| quizType | enum | true | 'chapter', 'module', atau 'final' |
| chapterId | number | false | ID chapter (untuk chapter quiz) |
| moduleId | number | false | ID module (untuk module quiz) |
| courseId | number | true | ID course |
| minimumScore | number | true | Minimum score untuk lulus (0-100) |
| timeLimit | number | false | Batas waktu dalam menit |
| questions | array | true | Array pertanyaan |

**Submit Quiz Attempt**
```
POST /api/quizzes/[id]/submit
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| answers | array | true | Array jawaban student |
| startedAt | string | true | Waktu mulai quiz (ISO format) |
| completedAt | string | true | Waktu selesai quiz (ISO format) |

### 4.5 Progress Tracking APIs

**Get Student Progress**
```
GET /api/progress/student/[studentId]/course/[courseId]
```

Response:
| Param Name | Param Type | Description |
|------------|------------|-------------|
| courseProgress | object | Overall course progress |
| moduleProgress | array | Progress per module |
| chapterProgress | array | Progress per chapter |
| quizAttempts | array | History quiz attempts |
| unlockedContent | object | Content yang sudah unlocked |

**Mark Content as Complete**
```
POST /api/progress/complete
```

Request:
| Param Name | Param Type | isRequired | Description |
|------------|------------|------------|-------------|
| studentId | number | true | ID student |
| courseId | number | true | ID course |
| chapterId | number | false | ID chapter (jika complete chapter) |
| moduleId | number | false | ID module (jika complete module) |
| completedAt | string | true | Waktu completion (ISO format) |

## 5. Server Architecture Diagram

```mermaid
graph TD
    A[Client Request] --> B[Next.js API Route Handler]
    B --> C[Request Validation Layer]
    C --> D[Business Logic Layer]
    D --> E[Data Access Layer - Drizzle ORM]
    E --> F[(Neon PostgreSQL)]
    
    D --> G[Progress Calculation Service]
    D --> H[Quiz Scoring Service]
    D --> I[Content Unlocking Service]
    
    subgraph "API Layer"
        B
        C
    end
    
    subgraph "Business Logic"
        D
        G
        H
        I
    end
    
    subgraph "Data Layer"
        E
        F
    end
```

## 6. Data Model

### 6.1 Data Model Definition

```mermaid
erDiagram
    COURSES ||--o{ MODULES : contains
    MODULES ||--o{ CHAPTERS : contains
    CHAPTERS ||--o{ QUIZZES : "chapter quiz"
    MODULES ||--o{ QUIZZES : "module quiz"
    COURSES ||--o{ QUIZZES : "final exam"
    QUIZZES ||--o{ QUESTIONS : contains
    QUIZZES ||--o{ QUIZ_ATTEMPTS : attempted_by
    USERS ||--o{ QUIZ_ATTEMPTS : attempts
    USERS ||--o{ STUDENT_PROGRESS : tracks
    COURSES ||--o{ STUDENT_PROGRESS : "progress in"
    MODULES ||--o{ STUDENT_PROGRESS : "progress in"
    CHAPTERS ||--o{ STUDENT_PROGRESS : "progress in"
    
    COURSES {
        int id PK
        string name
        text description
        enum type
        timestamp startDate
        timestamp endDate
        int teacherId FK
        int institutionId FK
        string courseCode
        text coverPicture
    }
    
    MODULES {
        int id PK
        string name
        text description
        int courseId FK
        int orderIndex
        timestamp startDate
        timestamp endDate
    }
    
    CHAPTERS {
        int id PK
        string name
        text content
        int moduleId FK
        int orderIndex
    }
    
    QUIZZES {
        int id PK
        string name
        text description
        int chapterId FK
        int moduleId FK
        int courseId FK
        string quizType
        decimal minimumScore
        int timeLimit
        boolean isActive
    }
    
    QUESTIONS {
        int id PK
        int quizId FK
        enum type
        text question
        json options
        text correctAnswer
        decimal points
        int orderIndex
    }
    
    QUIZ_ATTEMPTS {
        int id PK
        int studentId FK
        int quizId FK
        decimal score
        decimal totalPoints
        boolean passed
        timestamp startedAt
        timestamp completedAt
        json answers
    }
    
    STUDENT_PROGRESS {
        int id PK
        int studentId FK
        int courseId FK
        int moduleId FK
        int chapterId FK
        boolean completed
        timestamp completedAt
    }
```

### 6.2 Data Definition Language

**Enhanced Courses Table**
```sql
-- Courses table sudah ada, tidak perlu perubahan
CREATE TABLE courses (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    type course_type NOT NULL DEFAULT 'self_paced',
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    teacher_id INTEGER REFERENCES users(id) NOT NULL,
    institution_id INTEGER REFERENCES institutions(id) NOT NULL,
    course_code VARCHAR(50) UNIQUE,
    cover_picture TEXT,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Index untuk performance
CREATE INDEX idx_courses_teacher_id ON courses(teacher_id);
CREATE INDEX idx_courses_institution_id ON courses(institution_id);
CREATE INDEX idx_courses_course_code ON courses(course_code);
```

**Modules Table**
```sql
-- Modules table sudah ada, tidak perlu perubahan
CREATE TABLE modules (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    course_id INTEGER REFERENCES courses(id) NOT NULL,
    order_index INTEGER NOT NULL,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Index untuk performance
CREATE INDEX idx_modules_course_id ON modules(course_id);
CREATE INDEX idx_modules_order ON modules(course_id, order_index);
```

**Chapters Table**
```sql
-- Chapters table sudah ada, tidak perlu perubahan
CREATE TABLE chapters (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    content TEXT,
    module_id INTEGER REFERENCES modules(id) NOT NULL,
    order_index INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Index untuk performance
CREATE INDEX idx_chapters_module_id ON chapters(module_id);
CREATE INDEX idx_chapters_order ON chapters(module_id, order_index);
```

**Enhanced Quizzes Table**
```sql
-- Quizzes table sudah ada, tidak perlu perubahan
CREATE TABLE quizzes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    chapter_id INTEGER REFERENCES chapters(id),
    module_id INTEGER REFERENCES modules(id),
    course_id INTEGER REFERENCES courses(id),
    quiz_type VARCHAR(50) NOT NULL DEFAULT 'chapter',
    minimum_score DECIMAL(5,2) NOT NULL DEFAULT 70,
    time_limit INTEGER,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Index untuk performance
CREATE INDEX idx_quizzes_chapter_id ON quizzes(chapter_id);
CREATE INDEX idx_quizzes_module_id ON quizzes(module_id);
CREATE INDEX idx_quizzes_course_id ON quizzes(course_id);
CREATE INDEX idx_quizzes_type ON quizzes(quiz_type);
```

**Student Progress Table**
```sql
-- Student Progress table sudah ada, tidak perlu perubahan
CREATE TABLE student_progress (
    id SERIAL PRIMARY KEY,
    student_id INTEGER REFERENCES users(id) NOT NULL,
    course_id INTEGER REFERENCES courses(id) NOT NULL,
    module_id INTEGER REFERENCES modules(id),
    chapter_id INTEGER REFERENCES chapters(id),
    completed BOOLEAN DEFAULT false,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Index untuk performance
CREATE INDEX idx_student_progress_student_course ON student_progress(student_id, course_id);
CREATE INDEX idx_student_progress_module ON student_progress(student_id, module_id);
CREATE INDEX idx_student_progress_chapter ON student_progress(student_id, chapter_id);
```

**Quiz Attempts Table**
```sql
-- Quiz Attempts table sudah ada, tidak perlu perubahan
CREATE TABLE quiz_attempts (
    id SERIAL PRIMARY KEY,
    student_id INTEGER REFERENCES users(id) NOT NULL,
    quiz_id INTEGER REFERENCES quizzes(id) NOT NULL,
    score DECIMAL(5,2),
    total_points DECIMAL(5,2),
    passed BOOLEAN DEFAULT false,
    started_at TIMESTAMP DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP,
    answers JSON,
    created_at TIMESTAMP DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP DEFAULT NOW() NOT NULL
);

-- Index untuk performance
CREATE INDEX idx_quiz_attempts_student_quiz ON quiz_attempts(student_id, quiz_id);
CREATE INDEX idx_quiz_attempts_quiz_id ON quiz_attempts(quiz_id);
CREATE INDEX idx_quiz_attempts_completed ON quiz_attempts(completed_at DESC);
```

## 7. Progressive Unlocking Algorithm

### 7.1 Content Unlocking Logic

```typescript
// Service untuk menentukan content yang bisa diakses student
class ContentUnlockingService {
  async getUnlockedContent(studentId: number, courseId: number) {
    const progress = await this.getStudentProgress(studentId, courseId);
    const courseStructure = await this.getCourseStructure(courseId);
    
    return {
      unlockedModules: this.calculateUnlockedModules(progress, courseStructure),
      unlockedChapters: this.calculateUnlockedChapters(progress, courseStructure),
      unlockedQuizzes: this.calculateUnlockedQuizzes(progress, courseStructure)
    };
  }
  
  private calculateUnlockedModules(progress: StudentProgress[], courseStructure: CourseStructure) {
    // Module pertama selalu unlocked
    // Module selanjutnya unlocked jika module quiz sebelumnya sudah passed
    const unlockedModules = [courseStructure.modules[0].id];
    
    for (let i = 1; i < courseStructure.modules.length; i++) {
      const previousModule = courseStructure.modules[i - 1];
      const previousModuleQuizPassed = this.isModuleQuizPassed(previousModule.id, progress);
      
      if (previousModuleQuizPassed) {
        unlockedModules.push(courseStructure.modules[i].id);
      } else {
        break; // Stop jika ada module yang belum selesai
      }
    }
    
    return unlockedModules;
  }
  
  private calculateUnlockedChapters(progress: StudentProgress[], courseStructure: CourseStructure) {
    const unlockedChapters = [];
    
    for (const module of courseStructure.modules) {
      if (module.chapters.length > 0) {
        // Chapter pertama dalam module selalu unlocked (jika modulenya unlocked)
        unlockedChapters.push(module.chapters[0].id);
        
        // Chapter selanjutnya unlocked jika chapter quiz sebelumnya passed
        for (let i = 1; i < module.chapters.length; i++) {
          const previousChapter = module.chapters[i - 1];
          const previousChapterQuizPassed = this.isChapterQuizPassed(previousChapter.id, progress);
          
          if (previousChapterQuizPassed) {
            unlockedChapters.push(module.chapters[i].id);
          } else {
            break;
          }
        }
      }
    }
    
    return unlockedChapters;
  }
  
  private calculateUnlockedQuizzes(progress: StudentProgress[], courseStructure: CourseStructure) {
    const unlockedQuizzes = [];
    
    // Chapter quizzes: unlocked setelah chapter content dibaca
    // Module quizzes: unlocked setelah semua chapter quizzes dalam module passed
    // Final exam: unlocked setelah semua module quizzes passed
    
    return unlockedQuizzes;
  }
}
```

### 7.2 Quiz Scoring Algorithm

```typescript
class QuizScoringService {
  async calculateQuizScore(quizAttempt: QuizAttemptData) {
    const quiz = await this.getQuizWithQuestions(quizAttempt.quizId);
    let totalScore = 0;
    let totalPoints = 0;
    
    for (const question of quiz.questions) {
      totalPoints += question.points;
      
      const studentAnswer = quizAttempt.answers.find(a => a.questionId === question.id);
      if (this.isAnswerCorrect(question, studentAnswer)) {
        totalScore += question.points;
      }
    }
    
    const percentage = (totalScore / totalPoints) * 100;
    const passed = percentage >= quiz.minimumScore;
    
    return {
      score: totalScore,
      totalPoints,
      percentage,
      passed
    };
  }
  
  private isAnswerCorrect(question: Question, studentAnswer: StudentAnswer): boolean {
    switch (question.type) {
      case 'multiple_choice':
        return question.correctAnswer === studentAnswer.answer;
      case 'true_false':
        return question.correctAnswer.toLowerCase() === studentAnswer.answer.toLowerCase();
      case 'essay':
        // Essay questions require manual grading
        return false;
      default:
        return false;
    }
  }
}
```

## 8. Performance Considerations

### 8.1 Database Optimization
- Proper indexing pada foreign keys dan frequently queried columns
- Query optimization dengan selective loading
- Connection pooling untuk database connections

### 8.2 Frontend Optimization
- Lazy loading untuk course content
- Virtualization untuk large lists (modules/chapters)
- Optimistic updates untuk better UX
- Caching dengan React Query atau SWR

### 8.3 Scalability
- Pagination untuk large datasets
- Background processing untuk heavy operations
- CDN untuk static assets (images, videos)
- Database read replicas untuk analytics queries

Dokumentasi teknis ini memberikan blueprint lengkap untuk implementasi Course Management yang terintegrasi dengan fokus pada performance, scalability, dan maintainability.