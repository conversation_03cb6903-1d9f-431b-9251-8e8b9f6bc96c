import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Trophy, CheckCircle, XCircle } from 'lucide-react';
import { Course } from '@/types/lms';

interface ExamTabProps {
  courseData: Course;
  onStartQuiz: (quizId: string) => void;
}

export const ExamTab: React.FC<ExamTabProps> = ({
  courseData,
  onStartQuiz
}) => {
  const isFinalExamUnlocked = courseData.modules.every(
    (m) =>
      m.chapters.every(
        (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
      ) && m.moduleQuiz.isPassed
  );

  return (
    <Card className='shadow-sm'>
      <CardHeader>
        <CardTitle className='flex items-center space-x-2'>
          <Trophy className='h-6 w-6' />
          <span><PERSON><PERSON><PERSON></span>
        </CardTitle>
      </CardHeader>
      <CardContent className='p-6'>
        <div className='space-y-6'>
          {/* Exam Requirements */}
          <div className='rounded-lg border border-amber-200 bg-amber-50 p-4'>
            <h4 className='mb-2 font-semibold text-amber-800'>
              Persyaratan Ujian
            </h4>
            <ul className='space-y-1 text-sm text-amber-700'>
              <li>• Selesaikan semua modul dan lulus semua kuis modul</li>
              <li>
                • Nilai minimum lulus: {courseData.finalExam.minimumScore}%
              </li>
              <li>• Batas waktu: {courseData.finalExam.timeLimit} menit</li>
              <li>• Maksimal percobaan: {courseData.finalExam.maxAttempts}</li>
            </ul>
          </div>

          {/* Current Status */}
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
            <div className='space-y-4'>
              <h4 className='font-medium text-gray-900'>
                Status Prasyarat
              </h4>
              {courseData.modules.map((module) => (
                <div
                  key={module.id}
                  className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
                >
                  <span className='text-sm'>{module.title}</span>
                  {module.moduleQuiz.isPassed ? (
                    <CheckCircle className='h-5 w-5 text-green-600' />
                  ) : (
                    <XCircle className='h-5 w-5 text-red-500' />
                  )}
                </div>
              ))}
            </div>
            <div className='space-y-4'>
              <h4 className='font-medium text-gray-900'>Informasi Ujian</h4>
              <div className='space-y-3'>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-gray-600'>Percobaan Digunakan</span>
                  <span className='font-medium'>
                    {courseData.finalExam.attempts}/
                    {courseData.finalExam.maxAttempts}
                  </span>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-gray-600'>Skor Terakhir</span>
                  <span className='font-medium'>
                    {courseData.finalExam.lastScore
                      ? `${courseData.finalExam.lastScore}%`
                      : 'N/A'}
                  </span>
                </div>
                <div className='flex items-center justify-between'>
                  <span className='text-sm text-gray-600'>Status</span>
                  <Badge
                    variant={
                      courseData.finalExam.isPassed ? 'default' : 'outline'
                    }
                  >
                    {courseData.finalExam.isPassed ? 'Lulus' : 'Belum Diambil'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          {/* Exam Action */}
          <div className='border-t pt-4 text-center'>
            <Button
              size='lg'
              variant='iai'
              disabled={
                courseData.finalExam.attempts >=
                  courseData.finalExam.maxAttempts
              }
              onClick={() => onStartQuiz(courseData.finalExam.id)}
            >
              <Trophy className='mr-2 h-5 w-5' />
              {courseData.finalExam.attempts === 0
                ? 'Mulai Ujian Akhir'
                : 'Ulangi Ujian Akhir'}
            </Button>
            {!isFinalExamUnlocked && (
              <p className='mt-2 text-sm text-gray-500'>
                Selesaikan semua modul untuk membuka ujian akhir
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
