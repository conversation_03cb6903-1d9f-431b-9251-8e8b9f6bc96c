# Course Management UI/UX Specifications - Terang LMS

## 1. Product Overview

Sistem Course Management yang terintegrasi memungkinkan teacher untuk membuat course dengan struktur hierarchical (Course → Modules → Chapters → Quizzes) dan progressive unlocking system untuk student. Interface dirancang untuk memberikan pengalaman yang intuitif dalam pembuatan course yang kompleks dan pembelajaran yang terstruktur.

## 2. Core Features

### 2.1 User Roles

| Role | Registration Method | Core Permissions |
|------|---------------------|------------------|
| Teacher | Institution invitation | Dapat membuat course, modules, chapters, dan quizzes; mengelola student progress |
| Student | Course enrollment | Dapat mengakses course content sesuai progress, mengerjakan quiz, melihat hasil |
| Institution Manager | Super Admin assignment | Dapat mengelola teachers dan melihat analytics course |

### 2.2 Feature Module

Sistem Course Management terdiri dari halaman-halaman utama berikut:

1. **Course Creation Wizard**: multi-step form, module planning, structure preview
2. **Course Structure Management**: drag-drop interface, content organization, progress tracking
3. **Module Management**: chapter creation, content editor, quiz builder
4. **Chapter Content Editor**: markdown editor, media upload, quiz integration
5. **Quiz Builder**: question types, scoring setup, time limits
6. **Student Learning Interface**: progressive content, quiz taking, progress visualization
7. **Analytics Dashboard**: completion rates, quiz performance, student insights

### 2.3 Page Details

| Page Name | Module Name | Feature Description |
|-----------|-------------|---------------------|
| Course Creation Wizard | Basic Information Form | Input course name, description, type, dates, cover image dengan validation |
| Course Creation Wizard | Module Structure Planning | Add/remove modules, set order, define chapter count per module |
| Course Creation Wizard | Structure Preview | Visual preview course structure sebelum creation |
| Course Structure Management | Course Overview | Display course info, statistics, quick actions untuk edit |
| Course Structure Management | Module List Interface | Drag-drop reordering, expand/collapse modules, quick edit actions |
| Course Structure Management | Chapter Management | Add/edit chapters, content status indicators, quiz assignment |
| Module Management | Module Details Form | Edit module info, set dates, manage chapter sequence |
| Module Management | Chapter Creation | Bulk chapter creation, template selection, auto-naming |
| Module Management | Module Quiz Builder | Create module quiz, question management, scoring rules |
| Chapter Content Editor | Markdown Editor | Rich text editor dengan preview, syntax highlighting, auto-save |
| Chapter Content Editor | Media Manager | Upload images/videos, embed content, file organization |
| Chapter Content Editor | Chapter Quiz Integration | Link chapter quiz, preview quiz flow, scoring setup |
| Quiz Builder | Question Management | Add/edit questions, multiple question types, bulk import |
| Quiz Builder | Quiz Settings | Time limits, attempts, passing score, feedback options |
| Quiz Builder | Preview & Testing | Test quiz functionality, preview student view, validation |
| Student Learning Interface | Course Navigation | Module/chapter tree, progress indicators, unlocking status |
| Student Learning Interface | Content Viewer | Chapter content display, reading progress, navigation controls |
| Student Learning Interface | Quiz Taking | Question display, answer submission, timer, progress tracking |
| Student Learning Interface | Progress Dashboard | Overall progress, quiz scores, achievements, next steps |
| Analytics Dashboard | Course Analytics | Enrollment stats, completion rates, time spent analysis |
| Analytics Dashboard | Quiz Performance | Question analysis, common mistakes, difficulty assessment |
| Analytics Dashboard | Student Insights | Individual progress, struggling students, engagement metrics |

## 3. Core Process

### 3.1 Teacher Flow - Course Creation

1. **Course Planning**: Teacher memulai dengan Course Creation Wizard, mengisi informasi dasar course
2. **Structure Design**: Teacher mendesain struktur modules dan chapters menggunakan planning interface
3. **Content Creation**: Teacher membuat content untuk setiap chapter menggunakan markdown editor
4. **Quiz Development**: Teacher membuat quizzes untuk chapters, modules, dan final exam
5. **Review & Publish**: Teacher mereview seluruh course structure dan mempublish untuk students
6. **Monitoring**: Teacher memantau progress students melalui analytics dashboard

### 3.2 Student Flow - Learning Process

1. **Course Access**: Student mengakses course melalui enrollment atau course code
2. **Progressive Learning**: Student mengikuti learning path yang sudah ditentukan dengan unlocking system
3. **Content Consumption**: Student membaca chapter content dan menyelesaikan chapter quizzes
4. **Module Completion**: Setelah semua chapter quizzes passed, student dapat mengerjakan module quiz
5. **Course Completion**: Setelah semua module quizzes passed, student dapat mengerjakan final exam
6. **Progress Tracking**: Student dapat melihat progress dan achievements di dashboard

```mermaid
graph TD
    A[Course Creation Wizard] --> B[Module Structure Planning]
    B --> C[Chapter Content Creation]
    C --> D[Quiz Builder]
    D --> E[Course Review]
    E --> F[Course Published]
    
    G[Student Enrollment] --> H[Course Dashboard]
    H --> I[Chapter Content]
    I --> J[Chapter Quiz]
    J --> K{Quiz Passed?}
    K -->|Yes| L[Next Chapter Unlocked]
    K -->|No| M[Retake Quiz]
    M --> J
    L --> N{All Chapters Complete?}
    N -->|Yes| O[Module Quiz]
    N -->|No| I
    O --> P{Module Quiz Passed?}
    P -->|Yes| Q[Next Module Unlocked]
    P -->|No| R[Retake Module Quiz]
    R --> O
    Q --> S{All Modules Complete?}
    S -->|Yes| T[Final Exam]
    S -->|No| I
    T --> U[Course Completion]
```

## 4. User Interface Design

### 4.1 Design Style

- **Primary Colors**: 
  - Primary Blue: #3B82F6 (untuk actions dan navigation)
  - Success Green: #10B981 (untuk completed states)
  - Warning Orange: #F59E0B (untuk pending/in-progress states)
  - Error Red: #EF4444 (untuk failed states)
- **Secondary Colors**:
  - Gray Scale: #F9FAFB, #F3F4F6, #E5E7EB, #9CA3AF, #6B7280, #374151
  - Background: #FFFFFF dengan subtle gray backgrounds untuk sections
- **Typography**:
  - Primary Font: Inter (clean, modern, highly readable)
  - Headings: Font weights 600-700, sizes 24px-32px
  - Body Text: Font weight 400, size 16px, line-height 1.6
  - Code/Monospace: JetBrains Mono untuk code blocks
- **Button Styles**:
  - Primary: Rounded corners (8px), solid background, white text
  - Secondary: Outlined style dengan primary color border
  - Ghost: Text-only dengan hover background
- **Layout Style**:
  - Card-based design dengan subtle shadows
  - Sidebar navigation untuk main sections
  - Breadcrumb navigation untuk deep pages
  - Responsive grid system (12-column)
- **Icons**: 
  - Lucide React icons untuk consistency
  - 20px-24px sizes untuk most icons
  - Outlined style untuk better visibility

### 4.2 Page Design Overview

| Page Name | Module Name | UI Elements |
|-----------|-------------|-------------|
| Course Creation Wizard | Step Indicator | Horizontal stepper dengan 4 steps, current step highlighted dengan primary color, completed steps dengan checkmark |
| Course Creation Wizard | Form Layout | Two-column layout, left side untuk form fields, right side untuk preview/help text |
| Course Creation Wizard | Module Planning | Drag-drop interface dengan visual cards, add/remove buttons, reorder handles |
| Course Structure Management | Sidebar Navigation | Collapsible sidebar dengan course info, module tree, quick actions |
| Course Structure Management | Main Content Area | Tabbed interface (Overview, Modules, Analytics), breadcrumb navigation |
| Course Structure Management | Module Cards | Expandable cards dengan progress bars, chapter count, quiz indicators |
| Chapter Content Editor | Split Layout | Left: markdown editor dengan toolbar, Right: live preview dengan styling |
| Chapter Content Editor | Toolbar | Rich text formatting, media insert, save status, word count |
| Chapter Content Editor | Media Panel | Drag-drop upload area, thumbnail grid, file management |
| Quiz Builder | Question List | Sortable list dengan question types, points, difficulty indicators |
| Quiz Builder | Question Editor | Modal/slide-out panel dengan question type selector, rich text editor |
| Quiz Builder | Settings Panel | Collapsible settings dengan time limits, attempts, scoring options |
| Student Learning Interface | Progress Header | Course title, overall progress bar, current module/chapter indicator |
| Student Learning Interface | Content Navigation | Left sidebar dengan module tree, progress dots, locked/unlocked states |
| Student Learning Interface | Content Area | Clean reading interface, navigation buttons, reading progress indicator |
| Student Learning Interface | Quiz Interface | Question counter, timer, answer options, submit/next buttons |
| Analytics Dashboard | Metrics Cards | KPI cards dengan icons, numbers, trend indicators |
| Analytics Dashboard | Charts Section | Interactive charts dengan filters, date ranges, export options |
| Analytics Dashboard | Student Table | Sortable table dengan progress bars, quiz scores, last activity |

### 4.3 Component Specifications

#### 4.3.1 Course Creation Wizard Components

**Step Indicator Component**
```typescript
interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
  stepLabels: string[];
  completedSteps: number[];
}

// Visual: Horizontal line dengan circles untuk setiap step
// Active step: Primary color dengan white number
// Completed step: Success color dengan checkmark
// Pending step: Gray color dengan number
```

**Module Planning Component**
```typescript
interface ModulePlannerProps {
  modules: ModuleStructure[];
  onAddModule: () => void;
  onRemoveModule: (id: string) => void;
  onReorderModules: (newOrder: ModuleStructure[]) => void;
  onEditModule: (id: string, data: Partial<ModuleStructure>) => void;
}

// Visual: Vertical list dengan drag handles
// Each module card shows: name, chapter count, estimated duration
// Add button at bottom, remove button on hover
```

#### 4.3.2 Content Editor Components

**Markdown Editor Component**
```typescript
interface MarkdownEditorProps {
  content: string;
  onChange: (content: string) => void;
  onSave: () => void;
  autoSave?: boolean;
  placeholder?: string;
}

// Features: Syntax highlighting, live preview, toolbar
// Toolbar: Bold, italic, headers, lists, links, images, code blocks
// Auto-save indicator, word count, full-screen mode
```

**Media Upload Component**
```typescript
interface MediaUploadProps {
  onUpload: (files: File[]) => void;
  acceptedTypes: string[];
  maxSize: number;
  multiple?: boolean;
}

// Visual: Drag-drop zone dengan upload progress
// File preview dengan thumbnails
// Upload status indicators, error handling
```

#### 4.3.3 Quiz Components

**Question Builder Component**
```typescript
interface QuestionBuilderProps {
  question: Question;
  onChange: (question: Question) => void;
  questionTypes: QuestionType[];
}

// Dynamic form based on question type
// Multiple choice: options with add/remove
// True/False: simple toggle
// Essay: rich text area dengan rubric
```

**Quiz Taking Component**
```typescript
interface QuizTakingProps {
  quiz: Quiz;
  onSubmitAnswer: (questionId: string, answer: any) => void;
  onSubmitQuiz: () => void;
  timeRemaining?: number;
}

// Visual: Question counter, timer, progress bar
// Answer options based on question type
// Navigation: previous/next, review mode
```

#### 4.3.4 Progress Components

**Progress Tree Component**
```typescript
interface ProgressTreeProps {
  courseStructure: CourseStructure;
  studentProgress: StudentProgress;
  onNavigate: (chapterId: string) => void;
}

// Visual: Collapsible tree dengan icons
// Completed: Green checkmark
// Current: Blue highlight
// Locked: Gray dengan lock icon
// In Progress: Orange dot
```

**Progress Bar Component**
```typescript
interface ProgressBarProps {
  current: number;
  total: number;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'success' | 'warning';
}

// Visual: Rounded progress bar dengan smooth animations
// Percentage text overlay
// Different sizes dan colors untuk different contexts
```

### 4.4 Responsive Design

#### 4.4.1 Breakpoints
- **Mobile**: < 768px
- **Tablet**: 768px - 1024px  
- **Desktop**: > 1024px

#### 4.4.2 Mobile Adaptations

**Course Creation Wizard (Mobile)**
- Single column layout
- Step indicator menjadi vertical atau dots
- Module planning menggunakan accordion style
- Touch-friendly drag handles

**Content Editor (Mobile)**
- Tabbed interface (Edit/Preview)
- Floating toolbar
- Full-screen editing mode
- Swipe gestures untuk navigation

**Student Interface (Mobile)**
- Bottom navigation untuk main sections
- Collapsible progress sidebar
- Swipe untuk next/previous chapter
- Touch-optimized quiz interface

#### 4.4.3 Tablet Adaptations

**Course Management (Tablet)**
- Sidebar dapat di-collapse
- Two-column layout untuk forms
- Touch-friendly drag-drop
- Optimized untuk landscape orientation

**Content Creation (Tablet)**
- Split-screen editor/preview
- Floating toolbars
- Gesture support untuk common actions
- Optimized keyboard shortcuts

### 4.5 Accessibility Features

#### 4.5.1 Keyboard Navigation
- Tab order yang logical
- Keyboard shortcuts untuk common actions
- Focus indicators yang jelas
- Skip links untuk main content

#### 4.5.2 Screen Reader Support
- Semantic HTML structure
- ARIA labels dan descriptions
- Alt text untuk images
- Progress announcements

#### 4.5.3 Visual Accessibility
- High contrast mode support
- Scalable text (up to 200%)
- Color-blind friendly palette
- Clear visual hierarchy

### 4.6 Animation & Interactions

#### 4.6.1 Micro-interactions
- Button hover states dengan subtle scale
- Loading spinners untuk async operations
- Success animations untuk completed actions
- Smooth transitions untuk state changes

#### 4.6.2 Page Transitions
- Fade transitions untuk page changes
- Slide animations untuk wizard steps
- Expand/collapse animations untuk accordions
- Progress bar animations untuk completion

#### 4.6.3 Feedback Systems
- Toast notifications untuk actions
- Inline validation messages
- Progress indicators untuk long operations
- Confirmation dialogs untuk destructive actions

Dokumentasi UI/UX ini memberikan panduan lengkap untuk implementasi interface yang user-friendly, accessible, dan responsive untuk sistem Course Management yang terintegrasi.