import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { query } from '@/lib/db/raw';
import { ApiResponse } from '@/types/database';

// GET /api/users/[id] - Get single user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' } as ApiResponse,
        { status: 400 }
      );
    }

    const result = await query`
      SELECT
        u.id,
        u.name,
        u.email,
        u.role,
        u.institution_id,
        u.created_at,
        u.updated_at,
        i.name as institution_name,
        i.type as institution_type
      FROM users u
      LEFT JOIN institutions i ON u.institution_id = i.id
      WHERE u.id = ${id}
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'User retrieved successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Get user error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve user' } as ApiResponse,
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - Update user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    const body = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' } as ApiResponse,
        { status: 400 }
      );
    }

    const { name, email, password, role, institutionId } = body;

    // Check if user exists
    const existingUser = await query`
      SELECT id, email FROM users WHERE id = ${id}
    `;

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      );
    }

    // Check if email is being changed and if it already exists
    if (email && email !== existingUser[0].email) {
      const emailCheck = await query`
        SELECT id FROM users WHERE email = ${email} AND id != ${id}
      `;

      if (emailCheck.length > 0) {
        return NextResponse.json(
          { success: false, error: 'Email already exists' } as ApiResponse,
          { status: 400 }
        );
      }
    }

    // Prepare update fields
    let updateFields = [];
    let hashedPassword = null;

    if (password) {
      const saltRounds = 10;
      hashedPassword = await bcrypt.hash(password, saltRounds);
    }

    const result = await query`
      UPDATE users SET
        name = COALESCE(${name}, name),
        email = COALESCE(${email}, email),
        password = COALESCE(${hashedPassword}, password),
        role = COALESCE(${role}, role),
        institution_id = ${institutionId !== undefined ? institutionId : null},
        updated_at = NOW()
      WHERE id = ${id}
      RETURNING id, name, email, role, institution_id, created_at, updated_at
    `;

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'User updated successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update user' } as ApiResponse,
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - Delete user
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid user ID' } as ApiResponse,
        { status: 400 }
      );
    }

    // Check if user exists
    const existingUser = await query`
      SELECT id, role FROM users WHERE id = ${id}
    `;

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'User not found' } as ApiResponse,
        { status: 404 }
      );
    }

    // Prevent deletion of super_admin users (optional safety check)
    if (existingUser[0].role === 'super_admin') {
      return NextResponse.json(
        { success: false, error: 'Cannot delete super admin users' } as ApiResponse,
        { status: 403 }
      );
    }

    // Delete user
    await query`
      DELETE FROM users WHERE id = ${id}
    `;

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete user' } as ApiResponse,
      { status: 500 }
    );
  }
}