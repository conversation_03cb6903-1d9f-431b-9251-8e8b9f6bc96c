'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { authStorage, getRedirectPath } from '@/lib/auth';
import { Input } from '@/components/ui/input';
import { CheckCircle2 } from 'lucide-react'; // Import CheckCircle2 icon
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { ViewIcon as EyeIcon, ViewOffIcon as EyeOffIcon, Building02Icon as BuildingIcon, BookOpen01Icon as BookIcon, UserIcon, Mail01Icon as MailIcon, LockIcon, ArrowLeft02Icon as ChevronLeftIcon, ArrowRight02Icon as ChevronRightIcon, Award01Icon as AwardIcon, Video01Icon as VideoIcon, Task01Icon as TaskIcon } from 'hugeicons-react';
import Link from 'next/link';
import Image from 'next/image';

// Feature Carousel Component
const FeatureCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const features = [
    {
      icon: BookIcon,
      title: 'Pembelajaran Interaktif',
      description: 'Akses kursus video berkualitas tinggi dengan materi yang disusun oleh para ahli arsitektur Indonesia',
      highlight: '50+ Kursus Tersedia'
    },
    {
      icon: TaskIcon,
      title: 'Ujian Bersertifikat',
      description: 'Uji kompetensi Anda dengan sistem ujian online yang ketat dan dapatkan sertifikat resmi IAI',
      highlight: 'Sertifikat Diakui Nasional'
    },
    {
      icon: AwardIcon,
      title: 'Pengembangan Karir',
      description: 'Tingkatkan kredibilitas profesional dengan sertifikasi yang diakui industri arsitektur Indonesia',
      highlight: '95% Peserta Berhasil'
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [features.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % features.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + features.length) % features.length);
  };

  const currentFeature = features[currentSlide];
  const Icon = currentFeature.icon;

  return (
    <div className='bg-white/10 backdrop-blur-sm rounded-xl p-6 min-h-[200px]'>
      <div className='flex items-center justify-between mb-4'>
        <button
          onClick={prevSlide}
          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'
        >
          <ChevronLeftIcon className='w-4 h-4 text-white' />
        </button>
        
        <div className='flex space-x-2'>
          {features.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentSlide ? 'bg-white' : 'bg-white/40'
              }`}
            />
          ))}
        </div>
        
        <button
          onClick={nextSlide}
          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'
        >
          <ChevronRightIcon className='w-4 h-4 text-white' />
        </button>
      </div>

      <div className='text-center space-y-4'>
        <div className='flex justify-center'>
          <div className='w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center'>
            <Icon className='w-8 h-8 text-white' />
          </div>
        </div>
        
        <div className='space-y-2'>
          <h3 className='text-xl font-bold text-white'>{currentFeature.title}</h3>
          <p className='text-white/90 text-sm leading-relaxed'>{currentFeature.description}</p>
          <div className='inline-block bg-white/20 px-3 py-1 rounded-full'>
            <span className='text-xs font-medium text-white'>{currentFeature.highlight}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function SignInViewPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loginSuccess, setLoginSuccess] = useState(false); // New state for login success animation
  const [error, setError] = useState('');
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Login failed');
      }

      // Store user data using authStorage for consistency
      authStorage.setUser(data.user);
      
      setLoginSuccess(true); // Set success state to true
      toast.success('Login berhasil!', {
        description: `Selamat datang kembali, ${data.user.name}`,
        duration: 3000,
      });
      
      // Redirect to dashboard or home page based on user role after a short delay
      setTimeout(() => {
        const redirectPath = getRedirectPath(data.user);
        console.log('User role:', data.user.role, 'Redirecting to:', redirectPath);
        window.location.href = redirectPath;
      }, 1500);
      
    } catch (err: any) {
      setError(err.message || 'Terjadi kesalahan saat login');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='h-screen bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden'>
      <div className='flex h-screen'>
        {/* Left Side - Branding and Information */}
        <div className='hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-slate-800 to-slate-900 text-white'>
          {/* Background Pattern */}
          <div className='absolute inset-0 opacity-10'>
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
                  <path d="M 60 0 L 0 0 0 60" fill="none" stroke="currentColor" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>

          <div className='relative z-10 flex flex-col justify-between p-12 w-full'>
            {/* Logo and Branding */}
            <div className='space-y-4'>
              <div className='flex justify-center'>
                <Image
                  src='/assets/logo-iai-putih.png'
                  alt='IAI Logo'
                  width={200}
                  height={200}
                  className='object-contain'
                />
              </div>
              
              <div className='space-y-6 mt-12'>
                <h2 className='text-3xl font-bold leading-tight'>
                  Selamat Datang di<br />
                  Sistem Pembelajaran<br />
                  Profesional
                </h2>
                <p className='text-lg text-white/90 leading-relaxed'>
                  Platform pembelajaran online khusus untuk para arsitek profesional. 
                  Tingkatkan keahlian Anda dengan kursus berkualitas tinggi.
                </p>
              </div>

              {/* Features Carousel */}
              <div className='relative mt-8'>
                <FeatureCarousel />
              </div>
            </div>

          </div>
        </div>

        {/* Right Side - Login Form */}
        <div className='flex-1 overflow-y-auto'>
          <div className='flex min-h-full items-center justify-center p-4 sm:p-8'>
            <div className='w-full max-w-md space-y-4 sm:space-y-6 py-4'>
            {/* Mobile Logo */}
            <div className='lg:hidden text-center mb-8'>
              <div className='flex justify-center mb-4'>
                <Image
                  src='/assets/logo-iai.png'
                  alt='IAI Logo'
                  width={200}
                  height={200}
                  className='object-contain'
                />
              </div>
            </div>

            {/* Login Card */}
            <Card className='shadow-xl border-0'>
              <CardHeader className='text-center space-y-1 pb-6'>
                <CardTitle className='text-2xl font-bold text-gray-900'>Masuk ke Akun</CardTitle>
                <CardDescription className='text-gray-600'>
                  Masukkan email dan password untuk mengakses kursus Anda
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {error && (
                  <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-md'>
                    <p className='text-sm text-red-600'>{error}</p>
                  </div>
                )}

                <form onSubmit={handleSubmit} className='space-y-4'>
                  {/* Email Field */}
                  <div className='space-y-2'>
                    <Label htmlFor='email' className='text-sm font-medium text-gray-700'>
                      Email
                    </Label>
                    <div className='relative'>
                      <MailIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
                      <Input
                        id='email'
                        name='email'
                        type='email'
                        placeholder='<EMAIL>'
                        value={formData.email}
                        onChange={handleInputChange}
                        className='pl-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                    </div>
                  </div>

                  {/* Password Field */}
                  <div className='space-y-2'>
                    <Label htmlFor='password' className='text-sm font-medium text-gray-700'>
                      Password
                    </Label>
                    <div className='relative'>
                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
                      <Input
                        id='password'
                        name='password'
                        type={showPassword ? 'text' : 'password'}
                        placeholder='Masukkan password'
                        value={formData.password}
                        onChange={handleInputChange}
                        className='pl-10 pr-10 h-12 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                      <button
                        type='button'
                        onClick={() => setShowPassword(!showPassword)}
                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                      >
                        {showPassword ? (
                          <EyeOffIcon className='w-4 h-4' />
                        ) : (
                          <EyeIcon className='w-4 h-4' />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Remember Me and Forgot Password */}
                  <div className='flex items-center justify-between pt-2'>
                    <div className='flex items-center space-x-2'>
                      <Checkbox
                        id='rememberMe'
                        name='rememberMe'
                        checked={formData.rememberMe}
                        onCheckedChange={(checked) => 
                          setFormData(prev => ({ ...prev, rememberMe: checked as boolean }))
                        }
                        className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)]'
                      />
                      <Label 
                        htmlFor='rememberMe' 
                        className='text-sm text-gray-600 cursor-pointer'
                      >
                        Ingat saya
                      </Label>
                    </div>
                    <Link
                      href='/auth/forgot-password'
                      className='text-sm text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium'
                    >
                      Lupa password?
                    </Link>
                  </div>

                  {/* Login Button */}
                  <Button
                    type='submit'
                    variant='iai'
                    className='w-full h-12 text-base font-medium mt-6'
                    disabled={loading || loginSuccess} // Disable button during loading or success animation
                  >
                    {loading ? (
                      'Memproses...'
                    ) : loginSuccess ? (
                      <span className="flex items-center justify-center">
                        <CheckCircle2 className="mr-2 h-5 w-5" /> Berhasil!
                      </span>
                    ) : (
                      'Masuk ke Dashboard'
                    )}
                  </Button>
                </form>

                {/* Divider */}
                <div className='relative my-6'>
                  <div className='absolute inset-0 flex items-center'>
                    <span className='w-full border-t border-gray-200' />
                  </div>
                  <div className='relative flex justify-center text-xs uppercase'>
                    <span className='bg-white px-2 text-gray-500'>atau</span>
                  </div>
                </div>

                {/* Alternative Login Methods */}
                <div className='space-y-3'>
                  <Button
                    variant='outline'
                    className='w-full h-11 font-medium border-gray-200 hover:bg-gray-50'
                  >
                    <svg className='w-4 h-4 mr-2' viewBox='0 0 24 24'>
                      <path fill='#4285F4' d='M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z'/>
                      <path fill='#34A853' d='M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z'/>
                      <path fill='#FBBC05' d='M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z'/>
                      <path fill='#EA4335' d='M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z'/>
                    </svg>
                    Masuk dengan Google
                  </Button>
                </div>

                {/* Sign Up Link */}
                <p className='text-center text-sm text-gray-600 mt-6'>
                  Belum punya akun?{' '}
                  <Link
                    href='/auth/sign-up'
                    className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium'
                  >
                    Daftar sekarang
                  </Link>
                </p>
              </CardContent>
            </Card>

            {/* Footer */}
            <div className='text-center text-xs text-gray-500 space-y-1'>
              <p>
                Dengan masuk, Anda menyetujui{' '}
                <Link href='/terms' className='underline hover:text-gray-700'>
                  Syarat & Ketentuan
                </Link>{' '}
                dan{' '}
                <Link href='/privacy' className='underline hover:text-gray-700'>
                  Kebijakan Privasi
                </Link>
              </p>
              <div className='flex items-center justify-center space-x-2 text-gray-400'>
                <span>© 2024 IAI LMS - Powered by</span>
                <img
                  src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
                  alt="Terang AI"
                  className='h-4 inline-block'
                />
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}