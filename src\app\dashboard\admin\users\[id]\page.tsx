'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save, Loader2, Eye, EyeOff } from 'lucide-react';
import Link from 'next/link';
import { useToast } from '@/hooks/use-toast';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  institution_id: number | null;
  institution_name: string | null;
  created_at: string;
  updated_at: string;
}

interface Institution {
  id: number;
  name: string;
}

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [institutions, setInstitutions] = useState<Institution[]>([]);
  const [dataLoaded, setDataLoaded] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    role: 'student',
    institutionId: ''
  });

  // Fetch institutions for dropdown
  useEffect(() => {
    const fetchInstitutions = async () => {
      try {
        const response = await fetch('/api/institutions');
        const data = await response.json();
        if (data.success) {
          setInstitutions(data.data.institutions);
        }
      } catch (error) {
        console.error('Error fetching institutions:', error);
      }
    };

    fetchInstitutions();
  }, []);

  // Fetch user data
  useEffect(() => {
    if (!userId || dataLoaded) return;

    const fetchUser = async () => {
      try {
        const response = await fetch(`/api/users/${userId}`);
        const data = await response.json();

        if (data.success) {
          const userData = data.data;
          setUser(userData);

          if (!dataLoaded) {
            setFormData({
              name: userData.name,
              email: userData.email,
              password: '', // Don't populate password for security
              role: userData.role,
              institutionId: userData.institution_id ? userData.institution_id.toString() : 'none'
            });
            setDataLoaded(true);
          }
        } else {
          toast({
            title: 'Error',
            description: data.error || 'Failed to fetch user',
            variant: 'destructive'
          });
          router.push('/dashboard/admin/users');
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch user',
          variant: 'destructive'
        });
        router.push('/dashboard/admin/users');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [userId, dataLoaded, router, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const submitData: any = {
        name: formData.name,
        email: formData.email,
        role: formData.role,
        institutionId: formData.institutionId && formData.institutionId !== 'none' ? parseInt(formData.institutionId) : null
      };

      // Only include password if it's provided
      if (formData.password.trim()) {
        submitData.password = formData.password;
      }

      const response = await fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'User updated successfully'
        });
        router.push('/dashboard/admin/users');
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to update user',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error updating user:', error);
      toast({
        title: 'Error',
        description: 'Failed to update user',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  const generatePassword = () => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*';
    let password = '';
    for (let i = 0; i < 12; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    handleInputChange('password', password);
  };

  if (loading) {
    return (
      <div className='flex min-h-[400px] items-center justify-center'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <p className='ml-2'>Loading user...</p>
      </div>
    );
  }

  if (!user) {
    return (
      <div className='py-8 text-center'>
        <p>User not found</p>
        <Link href='/dashboard/admin/users'>
          <Button className='mt-4'>Back to Users</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/admin/users'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Edit User
          </h1>
          <p className='text-muted-foreground'>
            Update user information for {user.name}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>User Details</CardTitle>
          <CardDescription>
            Update the information for {user.name}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Full Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder='Enter full name'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='email'>Email Address</Label>
                <Input
                  id='email'
                  type='email'
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder='Enter email address'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='password'>New Password (Optional)</Label>
                <div className='relative'>
                  <Input
                    id='password'
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    placeholder='Leave empty to keep current password'
                    className='pr-20'
                  />
                  <div className='absolute inset-y-0 right-0 flex items-center space-x-1 pr-2'>
                    <Button
                      type='button'
                      variant='ghost'
                      size='sm'
                      onClick={() => setShowPassword(!showPassword)}
                      className='h-7 w-7 p-0'
                    >
                      {showPassword ? (
                        <EyeOff className='h-4 w-4' />
                      ) : (
                        <Eye className='h-4 w-4' />
                      )}
                    </Button>
                  </div>
                </div>
                <Button
                  type='button'
                  variant='outline'
                  size='sm'
                  onClick={generatePassword}
                  className='mt-2'
                >
                  Generate New Password
                </Button>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='role'>User Role</Label>
                <Select
                  value={formData.role}
                  onValueChange={(value) => handleInputChange('role', value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select user role' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='student'>Student</SelectItem>
                    <SelectItem value='teacher'>Teacher</SelectItem>
                    <SelectItem value='super_admin'>Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2 md:col-span-2'>
                <Label htmlFor='institution'>Institution</Label>
                <Select
                  value={formData.institutionId}
                  onValueChange={(value) => handleInputChange('institutionId', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select institution (optional)' />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='none'>No Institution</SelectItem>
                    {institutions.map((institution) => (
                      <SelectItem key={institution.id} value={institution.id.toString()}>
                        {institution.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* User Information */}
            <Card>
              <CardHeader>
                <CardTitle className='text-lg'>User Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <div>
                    <h4 className='mb-2 font-semibold'>Account Details:</h4>
                    <div className='space-y-1 text-sm text-muted-foreground'>
                      <p>User ID: {user.id}</p>
                      <p>Created: {new Date(user.created_at).toLocaleDateString()}</p>
                      <p>Last Updated: {new Date(user.updated_at).toLocaleDateString()}</p>
                      <p>Current Institution: {user.institution_name || 'None'}</p>
                    </div>
                  </div>
                  <div>
                    <h4 className='mb-2 font-semibold'>Role Permissions:</h4>
                    <div className='space-y-1 text-sm text-muted-foreground'>
                      {formData.role === 'student' && (
                        <>
                          <p>• Can enroll in courses</p>
                          <p>• Can take quizzes and assignments</p>
                          <p>• Can view progress and grades</p>
                        </>
                      )}
                      {formData.role === 'teacher' && (
                        <>
                          <p>• Can create and manage courses</p>
                          <p>• Can create quizzes and assignments</p>
                          <p>• Can grade student submissions</p>
                        </>
                      )}
                      {formData.role === 'super_admin' && (
                        <>
                          <p>• Full platform access</p>
                          <p>• Can manage all institutions and users</p>
                          <p>• Can access billing and analytics</p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <div className='flex justify-end space-x-4'>
              <Link href='/dashboard/admin/users'>
                <Button variant='outline' type='button'>
                  Cancel
                </Button>
              </Link>
              <Button type='submit' disabled={isLoading}>
                <Save className='mr-2 h-4 w-4' />
                {isLoading ? 'Updating...' : 'Update User'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}