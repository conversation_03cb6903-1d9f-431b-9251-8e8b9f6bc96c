import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db/raw';
import { ApiResponse } from '@/types/database';

// GET /api/institutions/[id] - Get single institution
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid institution ID' } as ApiResponse,
        { status: 400 }
      );
    }

    const result = await query`
      SELECT 
        id,
        name,
        type,
        subscription_plan,
        billing_cycle,
        payment_status,
        payment_due_date,
        student_count,
        teacher_count,
        created_at,
        updated_at
      FROM institutions
      WHERE id = ${id}
    `;

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Institution not found' } as ApiResponse,
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'Institution retrieved successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Get institution error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to retrieve institution'
      } as ApiResponse,
      { status: 500 }
    );
  }
}

// PUT /api/institutions/[id] - Update institution
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);
    const body = await request.json();

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid institution ID' } as ApiResponse,
        { status: 400 }
      );
    }

    const {
      name,
      type,
      subscriptionPlan,
      billingCycle,
      studentCount,
      teacherCount,
      paymentStatus,
      paymentDueDate
    } = body;

    // Check if institution exists
    const existingResult = await query`
      SELECT id FROM institutions WHERE id = ${id}
    `;

    if (existingResult.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Institution not found' } as ApiResponse,
        { status: 404 }
      );
    }

    // Update institution with provided fields
    const result = await query`
      UPDATE institutions
      SET
        name = COALESCE(${name}, name),
        type = COALESCE(${type}, type),
        subscription_plan = COALESCE(${subscriptionPlan}, subscription_plan),
        billing_cycle = COALESCE(${billingCycle}, billing_cycle),
        student_count = COALESCE(${studentCount}, student_count),
        teacher_count = COALESCE(${teacherCount}, teacher_count),
        payment_status = COALESCE(${paymentStatus}, payment_status),
        payment_due_date = COALESCE(${paymentDueDate}, payment_due_date),
        updated_at = NOW()
      WHERE id = ${id}
      RETURNING *
    `;

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'Institution updated successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Update institution error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update institution' } as ApiResponse,
      { status: 500 }
    );
  }
}

// DELETE /api/institutions/[id] - Delete institution
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: idParam } = await params;
    const id = parseInt(idParam);

    if (isNaN(id)) {
      return NextResponse.json(
        { success: false, error: 'Invalid institution ID' } as ApiResponse,
        { status: 400 }
      );
    }

    // Check if institution exists
    const existingResult = await query`
      SELECT id FROM institutions WHERE id = ${id}
    `;

    if (existingResult.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Institution not found' } as ApiResponse,
        { status: 404 }
      );
    }

    // Check if institution has users or classes
    const dependenciesResult = await query`
      SELECT 
        (SELECT COUNT(*) FROM users WHERE institution_id = ${id}) as user_count,
        (SELECT COUNT(*) FROM classes WHERE institution_id = ${id}) as class_count,
        (SELECT COUNT(*) FROM courses WHERE institution_id = ${id}) as course_count
    `;

    const dependencies = dependenciesResult[0];
    if (
      dependencies.user_count > 0 ||
      dependencies.class_count > 0 ||
      dependencies.course_count > 0
    ) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Cannot delete institution with existing users, classes, or courses. Please remove them first.'
        } as ApiResponse,
        { status: 400 }
      );
    }

    await query`
      DELETE FROM institutions WHERE id = ${id}
    `;

    return NextResponse.json({
      success: true,
      message: 'Institution deleted successfully'
    } as ApiResponse);
  } catch (error) {
    console.error('Delete institution error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete institution' } as ApiResponse,
      { status: 500 }
    );
  }
}
