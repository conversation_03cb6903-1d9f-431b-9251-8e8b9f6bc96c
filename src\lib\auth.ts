import { AuthUser } from '@/types/database';

// Client-side auth utilities
export const authStorage = {
  setUser: (user: AuthUser) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_user', JSON.stringify(user));
    }
  },

  getUser: (): AuthUser | null => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('auth_user');
      return stored ? JSON.parse(stored) : null;
    }
    return null;
  },

  removeUser: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_user');
    }
  },

  isAuthenticated: (): boolean => {
    return authStorage.getUser() !== null;
  },

  hasRole: (role: string): boolean => {
    const user = authStorage.getUser();
    return user?.role === role;
  },

  isSuperAdmin: (): boolean => {
    return authStorage.hasRole('super_admin');
  },

  isTeacher: (): boolean => {
    return authStorage.hasRole('teacher');
  },

  isStudent: (): boolean => {
    return authStorage.hasRole('student');
  }
};

// Role-based redirect logic
export const getRedirectPath = (user: AuthUser): string => {
  switch (user.role) {
    case 'super_admin':
      return '/dashboard/admin';
    case 'teacher':
      return '/dashboard/teacher';
    case 'student':
      return '/courses';
    default:
      return '/dashboard';
  }
};

// Protected route checker
export const checkAuth = (): AuthUser | null => {
  const user = authStorage.getUser();
  if (!user) {
    // Redirect to sign in if not authenticated
    if (typeof window !== 'undefined') {
      window.location.href = '/auth/sign-in';
    }
    return null;
  }
  return user;
};

// Role-based access control
export const requireRole = (requiredRole: string): AuthUser | null => {
  const user = checkAuth();
  if (!user) return null;

  if (user.role !== requiredRole) {
    // Redirect to appropriate dashboard if wrong role
    if (typeof window !== 'undefined') {
      window.location.href = getRedirectPath(user);
    }
    return null;
  }
  return user;
};

// Multiple roles checker
export const requireAnyRole = (roles: string[]): AuthUser | null => {
  const user = checkAuth();
  if (!user) return null;

  if (!roles.includes(user.role)) {
    // Redirect to appropriate dashboard if wrong role
    if (typeof window !== 'undefined') {
      window.location.href = getRedirectPath(user);
    }
    return null;
  }
  return user;
};
