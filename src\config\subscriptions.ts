import { Shield, Crown, Sparkles } from 'lucide-react';
import { SubscriptionPlan } from '@/types/database';

export const subscriptionPlans: Record<string, SubscriptionPlan> = {
  basic: {
    name: 'Basic',
    pricePerStudent: {
      monthly: 5000,
      yearly: 45000 // 25% discount
    },
    features: [
      'Basic AI-powered learning',
      'Standard analytics',
      'Email support',
      'Basic question bank',
      '5 teachers per 100 students',
      'Standard reporting',
      'Basic classroom management'
    ],
    minStudents: 50,
    maxStudents: 200,
    isPopular: false,
    idealFor: 'Small Schools',
    icon: Shield,
    description:
      'Perfect for small schools starting their digital transformation journey'
  },
  pro: {
    name: 'Professional',
    pricePerStudent: {
      monthly: 8000,
      yearly: 72000 // 25% discount
    },
    features: [
      'Advanced AI learning paths',
      'Comprehensive analytics',
      'Priority support',
      'Extended question bank',
      'Custom branding',
      '15 teachers per 100 students',
      'Advanced reporting',
      'Parent portal access',
      'Integration with LMS'
    ],
    minStudents: 100,
    maxStudents: 1000,
    isPopular: true,
    idealFor: 'Growing Schools',
    icon: Crown,
    description:
      'Ideal for growing institutions seeking advanced features and scalability'
  },
  enterprise: {
    name: 'Enterprise',
    pricePerStudent: {
      monthly: 12000,
      yearly: 108000 // 25% discount
    },
    features: [
      'Full AI capabilities',
      'Enterprise analytics',
      '24/7 dedicated support',
      'Unlimited question bank',
      'API access',
      'Custom integrations',
      'Unlimited teachers',
      'Advanced security features',
      'Custom development options',
      'Dedicated account manager',
      'Multi-campus support'
    ],
    minStudents: 500,
    maxStudents: 5000,
    isPopular: false,
    idealFor: 'Large Institutions',
    icon: Sparkles,
    description: 'Enterprise-grade solution for large educational institutions'
  }
};

export const institutionTypes = [
  { value: 'sd-negeri', label: 'SD Negeri' },
  { value: 'sd-swasta', label: 'SD Swasta' },
  { value: 'smp-negeri', label: 'SMP Negeri' },
  { value: 'smp-swasta', label: 'SMP Swasta' },
  { value: 'sma-negeri', label: 'SMA Negeri' },
  { value: 'sma-swasta', label: 'SMA Swasta' },
  { value: 'university-negeri', label: 'University Negeri' },
  { value: 'university-swasta', label: 'University Swasta' },
  { value: 'institution-training', label: 'Training Institution' },
  { value: 'institution-course', label: 'Course Institution' },
  { value: 'institution-other', label: 'Other Institution' }
];

export const userRoles = [
  { value: 'student', label: 'Student/Participant' },
  { value: 'teacher', label: 'Teacher (Admin)' },
  { value: 'super_admin', label: 'Institution Manager (Super Admin)' }
];
