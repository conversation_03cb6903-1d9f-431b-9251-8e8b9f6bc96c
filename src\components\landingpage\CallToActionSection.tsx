'use client';

import { But<PERSON> } from '@/components/ui/button';
import {
  ArrowRight01Icon as ArrowRightIcon,
  CheckmarkCircle01Icon as CheckIcon,
  Award01Icon as AwardIcon,
  BookOpen01Icon as BookOpenIcon
} from 'hugeicons-react';

interface CallToActionSectionProps {
  onCTA?: () => void;
}

const benefits = [
  "Sertifikasi Profesional IAI",
  "Akses Seumur Hidup ke Materi Kursus",
  "Dukungan Instruktur Ahli",
  "Pengalaman Pembelajaran Interaktif",
  "Peluang Kemajuan Karir"
];

export default function CallToActionSection({ onCTA }: CallToActionSectionProps) {
  return (
    <section className="py-16 lg:py-24 bg-gradient-to-bl from-gray-900 via-gray-800 to-black relative overflow-hidden">
      {/* Background Pattern - Less white only */}
      <div className="absolute inset-0 bg-gradient-to-br from-transparent to-transparent" style={{ 
        background: 'radial-gradient(ellipse at center, rgba(255,255,255,0) 0%, transparent 90%)',
        opacity: 1
      }}></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Content */}
            <div className="text-white">
              <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Siap Mengubah Karir
                <span className="block text-yellow-300">Arsitektur Anda?</span>
              </h2>
              
              <p className="text-xl lg:text-2xl text-gray-200 mb-8 leading-relaxed">
                Bergabunglah dengan program sertifikasi Ikatan Arsitek Indonesia dan buka peluang baru dalam perjalanan profesional Anda.
              </p>

              <div className="space-y-4 mb-8">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckIcon className="w-6 h-6 text-green-400 flex-shrink-0" />
                    <span className="text-gray-200">{benefit}</span>
                  </div>
                ))}
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={onCTA}
                  className="bg-yellow-400 hover:bg-yellow-500 text-blue-900 font-bold text-lg px-8 py-4 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-200 flex items-center justify-center"
                >
                  Mulai Belajar Hari Ini
                  <ArrowRightIcon className="w-5 h-5 ml-2" />
                </button>
                
                <button
                  onClick={onCTA}
                  className="border-2 border-white text-white bg-transparent hover:bg-black hover:text-white font-bold text-lg px-8 py-4 rounded-xl transition-all duration-200 flex items-center justify-center"
                >
                  Jelajahi Kursus
                </button>
              </div>
            </div>

            {/* Visual Element */}
            <div className="flex justify-center lg:justify-end">
              <div className="relative">
                {/* Main Card */}
                <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full transform rotate-3 hover:rotate-0 transition-transform duration-300">
                  <div className="text-center">
                    <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                      <CheckIcon className="w-10 h-10 text-blue-600" />
                    </div>
                    
                    <h3 className="text-2xl font-bold text-gray-900 mb-4">
                      Sertifikat Profesional
                    </h3>
                    
                    <p className="text-gray-600 mb-6">
                      Dapatkan sertifikasi IAI Anda dan bergabung dengan jajaran arsitek profesional.
                    </p>
                    
                    <div className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-6 py-3 rounded-lg font-medium">
                      Sertifikat Diberikan
                    </div>
                  </div>
                </div>

                {/* Floating Elements */}
                <div className="absolute -top-4 -left-4 w-12 h-12 bg-yellow-400 rounded-full flex items-center justify-center shadow-lg animate-bounce">
                  <AwardIcon className="w-6 h-6 text-yellow-800" />
                </div>
                
                <div className="absolute -bottom-4 -right-4 w-12 h-12 bg-green-400 rounded-full flex items-center justify-center shadow-lg animate-pulse">
                  <CheckIcon className="w-6 h-6 text-green-800" />
                </div>
                
                <div className="absolute top-1/2 -left-8 w-8 h-8 bg-purple-400 rounded-full flex items-center justify-center shadow-lg">
                  <BookOpenIcon className="w-4 h-4 text-purple-800" />
                </div>
              </div>
            </div>
          </div>

          {/* Bottom Stats */}
          <div className="mt-16 pt-12 border-t border-blue-500 border-opacity-30">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center text-white">
              <div>
                <div className="text-3xl font-bold text-yellow-300 mb-2">Bergabung Sekarang</div>
                <div className="text-blue-200">Mulai perjalanan sertifikasi Anda hari ini</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-300 mb-2">Belajar</div>
                <div className="text-blue-200">Akses materi kursus yang komprehensif</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-yellow-300 mb-2">Berhasil</div>
                <div className="text-blue-200">Majukan karir profesional Anda</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}