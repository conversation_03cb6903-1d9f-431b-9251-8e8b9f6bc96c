'use client';

import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import {
  TrendingUp,
  BookOpen,
  Award,
  Clock,
  Target,
  CheckCircle
} from 'lucide-react';

export default function StudentProgressPage() {
  // Mock data - in real app, this would come from API
  const overallStats = {
    totalCourses: 3,
    completedCourses: 1,
    totalHours: 24,
    averageScore: 82,
    certificates: 1,
    currentStreak: 7
  };

  const courseProgress = [
    {
      id: 1,
      name: 'Introduction to Algebra',
      progress: 85,
      modules: [
        { name: 'Module 1', progress: 100, score: 92 },
        { name: 'Module 2', progress: 100, score: 88 },
        { name: 'Module 3', progress: 100, score: 95 },
        { name: 'Module 4', progress: 100, score: 85 },
        { name: 'Module 5', progress: 100, score: 90 },
        { name: 'Module 6', progress: 100, score: 87 },
        { name: 'Module 7', progress: 100, score: 93 },
        { name: 'Module 8', progress: 40, score: null }
      ],
      quizzes: [
        { name: 'Module 1 Quiz', score: 92, maxScore: 100, passed: true },
        { name: 'Module 2 Quiz', score: 88, maxScore: 100, passed: true },
        { name: 'Module 3 Quiz', score: 95, maxScore: 100, passed: true },
        { name: 'Module 4 Quiz', score: 85, maxScore: 100, passed: true },
        { name: 'Module 5 Quiz', score: 90, maxScore: 100, passed: true },
        { name: 'Module 6 Quiz', score: 87, maxScore: 100, passed: true },
        { name: 'Module 7 Quiz', score: 93, maxScore: 100, passed: true }
      ]
    },
    {
      id: 2,
      name: 'Physics Fundamentals',
      progress: 45,
      modules: [
        { name: 'Module 1', progress: 100, score: 78 },
        { name: 'Module 2', progress: 100, score: 82 },
        { name: 'Module 3', progress: 100, score: 75 },
        { name: 'Module 4', progress: 100, score: 88 },
        { name: 'Module 5', progress: 100, score: 80 },
        { name: 'Module 6', progress: 60, score: null },
        { name: 'Module 7', progress: 0, score: null },
        { name: 'Module 8', progress: 0, score: null }
      ],
      quizzes: [
        { name: 'Module 1 Quiz', score: 78, maxScore: 100, passed: true },
        { name: 'Module 2 Quiz', score: 82, maxScore: 100, passed: true },
        { name: 'Module 3 Quiz', score: 75, maxScore: 100, passed: true },
        { name: 'Module 4 Quiz', score: 88, maxScore: 100, passed: true },
        { name: 'Module 5 Quiz', score: 80, maxScore: 100, passed: true }
      ]
    },
    {
      id: 3,
      name: 'Chemistry Basics',
      progress: 100,
      modules: [
        { name: 'Module 1', progress: 100, score: 95 },
        { name: 'Module 2', progress: 100, score: 92 },
        { name: 'Module 3', progress: 100, score: 98 },
        { name: 'Module 4', progress: 100, score: 94 },
        { name: 'Module 5', progress: 100, score: 96 },
        { name: 'Module 6', progress: 100, score: 93 }
      ],
      quizzes: [
        { name: 'Module 1 Quiz', score: 95, maxScore: 100, passed: true },
        { name: 'Module 2 Quiz', score: 92, maxScore: 100, passed: true },
        { name: 'Module 3 Quiz', score: 98, maxScore: 100, passed: true },
        { name: 'Module 4 Quiz', score: 94, maxScore: 100, passed: true },
        { name: 'Module 5 Quiz', score: 96, maxScore: 100, passed: true },
        { name: 'Module 6 Quiz', score: 93, maxScore: 100, passed: true },
        { name: 'Final Exam', score: 95, maxScore: 100, passed: true }
      ]
    }
  ];

  const weeklyProgress = [
    { week: 'Week 1', hours: 4, score: 85 },
    { week: 'Week 2', hours: 6, score: 88 },
    { week: 'Week 3', hours: 3, score: 82 },
    { week: 'Week 4', hours: 5, score: 90 },
    { week: 'Week 5', hours: 4, score: 87 },
    { week: 'Week 6', hours: 2, score: 78 }
  ];

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Progress</h1>
          <p className='text-muted-foreground'>
            Track your learning journey and achievements
          </p>
        </div>
      </div>

      {/* Overview Stats */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-6'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Courses</CardTitle>
            <BookOpen className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.totalCourses}
            </div>
            <p className='text-muted-foreground text-xs'>
              {overallStats.completedCourses} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Study Hours</CardTitle>
            <Clock className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{overallStats.totalHours}</div>
            <p className='text-muted-foreground text-xs'>Total hours</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Average Score</CardTitle>
            <Target className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.averageScore}%
            </div>
            <p className='text-muted-foreground text-xs'>Across all quizzes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Certificates</CardTitle>
            <Award className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.certificates}
            </div>
            <p className='text-muted-foreground text-xs'>Earned</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Streak</CardTitle>
            <TrendingUp className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {overallStats.currentStreak}
            </div>
            <p className='text-muted-foreground text-xs'>Days active</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completion</CardTitle>
            <CheckCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>
              {Math.round(
                (overallStats.completedCourses / overallStats.totalCourses) *
                  100
              )}
              %
            </div>
            <p className='text-muted-foreground text-xs'>Overall progress</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='courses' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='courses'>Course Progress</TabsTrigger>
          <TabsTrigger value='analytics'>Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value='courses'>
          <div className='space-y-6'>
            {courseProgress.map((course) => (
              <Card key={course.id}>
                <CardHeader>
                  <div className='flex items-center justify-between'>
                    <div>
                      <CardTitle>{course.name}</CardTitle>
                      <CardDescription>
                        {
                          course.modules.filter((m) => m.progress === 100)
                            .length
                        }{' '}
                        of {course.modules.length} modules completed
                      </CardDescription>
                    </div>
                    <div className='text-right'>
                      <div className='text-2xl font-bold'>
                        {course.progress}%
                      </div>
                      <Badge
                        variant={
                          course.progress === 100 ? 'default' : 'secondary'
                        }
                      >
                        {course.progress === 100 ? 'Completed' : 'In Progress'}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <Progress value={course.progress} className='h-3' />

                    <div className='grid gap-4 md:grid-cols-2'>
                      {/* Module Progress */}
                      <div>
                        <h4 className='mb-3 font-semibold'>Module Progress</h4>
                        <div className='space-y-2'>
                          {course.modules.map((module, index) => (
                            <div
                              key={index}
                              className='flex items-center justify-between text-sm'
                            >
                              <span>{module.name}</span>
                              <div className='flex items-center space-x-2'>
                                <Progress
                                  value={module.progress}
                                  className='h-2 w-16'
                                />
                                <span className='w-12 text-right'>
                                  {module.progress}%
                                </span>
                                {module.score && (
                                  <Badge variant='outline' className='text-xs'>
                                    {module.score}%
                                  </Badge>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Quiz Results */}
                      <div>
                        <h4 className='mb-3 font-semibold'>Quiz Results</h4>
                        <div className='space-y-2'>
                          {course.quizzes.map((quiz, index) => (
                            <div
                              key={index}
                              className='flex items-center justify-between text-sm'
                            >
                              <span>{quiz.name}</span>
                              <div className='flex items-center space-x-2'>
                                <span>
                                  {quiz.score}/{quiz.maxScore}
                                </span>
                                <Badge
                                  variant={
                                    quiz.passed ? 'default' : 'destructive'
                                  }
                                >
                                  {quiz.passed ? 'Passed' : 'Failed'}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value='analytics'>
          <div className='grid gap-6 md:grid-cols-2'>
            <Card>
              <CardHeader>
                <CardTitle>Weekly Study Hours</CardTitle>
                <CardDescription>
                  Your study time over the past 6 weeks
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <BarChart data={weeklyProgress}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='week' />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey='hours' fill='#3b82f6' />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Score Trend</CardTitle>
                <CardDescription>
                  Your average quiz scores over time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width='100%' height={300}>
                  <LineChart data={weeklyProgress}>
                    <CartesianGrid strokeDasharray='3 3' />
                    <XAxis dataKey='week' />
                    <YAxis domain={[70, 100]} />
                    <Tooltip />
                    <Line
                      type='monotone'
                      dataKey='score'
                      stroke='#10b981'
                      strokeWidth={2}
                      dot={{ fill: '#10b981' }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
