'use client';

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  BookOpen,
  FileText,
  Clock,
  Award,
  Play,
  CheckCircle,
  Lock,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { use } from 'react';

// Define interfaces for better type safety
interface Chapter {
  id: number;
  name: string;
  description: string;
  isCompleted: boolean;
  isUnlocked: boolean;
  hasQuiz: boolean;
  quizId: number;
  quizCompleted: boolean;
  quizScore: number | null;
}

interface Module {
  id: number;
  name: string;
  description: string;
  isUnlocked: boolean;
  progress: number;
  chapters: Chapter[];
  hasModuleQuiz: boolean;
  moduleQuizId: number;
  moduleQuizCompleted: boolean;
  moduleQuizScore: number | null;
}

interface Course {
  id: number;
  name: string;
  description: string;
  type: string;
  courseCode: string;
  instructor: string;
  progress: number;
  modules: Module[];
  hasFinalExam: boolean;
  finalExamId: number;
  finalExamUnlocked: boolean;
  finalExamCompleted: boolean;
  finalExamScore: number | null;
}

export default function StudentCourseDetailPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  // Unwrap the Promise using React's use() hook
  const { id } = use(params);

  // Mock data - in real app, this would come from API
  const course: Course = {
    id: parseInt(id),
    name: 'Introduction to Algebra',
    description: 'Basic algebraic concepts and problem solving',
    type: 'self_paced',
    courseCode: 'MATH101',
    instructor: 'Dr. Smith',
    progress: 76,
    modules: [
      {
        id: 1,
        name: 'Introduction and Fundamentals',
        description: 'Basic concepts and introduction to algebra',
        isUnlocked: true,
        progress: 100,
        chapters: [
          {
            id: 1,
            name: 'Chapter 1: Overview',
            description: 'Introduction to algebraic thinking',
            isCompleted: true,
            isUnlocked: true,
            hasQuiz: true,
            quizId: 1,
            quizCompleted: true,
            quizScore: 92
          },
          {
            id: 2,
            name: 'Chapter 2: Basic Concepts',
            description: 'Fundamental algebraic principles',
            isCompleted: true,
            isUnlocked: true,
            hasQuiz: true,
            quizId: 2,
            quizCompleted: true,
            quizScore: 88
          },
          {
            id: 3,
            name: 'Chapter 3: Variables and Expressions',
            description: 'Working with variables and expressions',
            isCompleted: true,
            isUnlocked: true,
            hasQuiz: true,
            quizId: 3,
            quizCompleted: true,
            quizScore: 95
          }
        ],
        hasModuleQuiz: true,
        moduleQuizId: 4,
        moduleQuizCompleted: true,
        moduleQuizScore: 90
      },
      {
        id: 2,
        name: 'Core Algebraic Operations',
        description: 'Essential operations and manipulations',
        isUnlocked: true,
        progress: 60,
        chapters: [
          {
            id: 4,
            name: 'Chapter 4: Addition and Subtraction',
            description: 'Basic algebraic operations',
            isCompleted: true,
            isUnlocked: true,
            hasQuiz: true,
            quizId: 5,
            quizCompleted: true,
            quizScore: 85
          },
          {
            id: 5,
            name: 'Chapter 5: Multiplication and Division',
            description: 'Advanced algebraic operations',
            isCompleted: false,
            isUnlocked: true,
            hasQuiz: true,
            quizId: 6,
            quizCompleted: false,
            quizScore: null
          }
        ],
        hasModuleQuiz: true,
        moduleQuizId: 7,
        moduleQuizCompleted: false,
        moduleQuizScore: null
      },
      {
        id: 3,
        name: 'Advanced Topics',
        description: 'Complex algebraic concepts',
        isUnlocked: false,
        progress: 0,
        chapters: [
          {
            id: 6,
            name: 'Chapter 6: Equations',
            description: 'Solving algebraic equations',
            isCompleted: false,
            isUnlocked: false,
            hasQuiz: true,
            quizId: 8,
            quizCompleted: false,
            quizScore: null
          }
        ],
        hasModuleQuiz: true,
        moduleQuizId: 9,
        moduleQuizCompleted: false,
        moduleQuizScore: null
      }
    ],
    hasFinalExam: true,
    finalExamId: 10,
    finalExamUnlocked: false,
    finalExamCompleted: false,
    finalExamScore: null
  };

  const getChapterStatus = (chapter: Chapter) => {
    if (!chapter.isUnlocked) return 'locked';
    if (chapter.isCompleted && chapter.quizCompleted) return 'completed';
    if (chapter.isCompleted && !chapter.quizCompleted) return 'quiz-pending';
    return 'in-progress';
  };

  const getModuleStatus = (module: Module) => {
    if (!module.isUnlocked) return 'locked';
    const allChaptersCompleted = module.chapters.every(
      (c: Chapter) => c.isCompleted && c.quizCompleted
    );
    if (allChaptersCompleted && module.moduleQuizCompleted) return 'completed';
    if (allChaptersCompleted && !module.moduleQuizCompleted)
      return 'quiz-pending';
    return 'in-progress';
  };

  return (
    <div className='space-y-6'>
      {/* Course Header */}
      <Card>
        <CardHeader>
          <div className='flex items-start justify-between'>
            <div className='space-y-2'>
              <div className='flex items-center space-x-2'>
                <h1 className='text-2xl font-bold'>{course.name}</h1>
                <Badge variant='outline'>{course.type}</Badge>
              </div>
              <p className='text-muted-foreground'>{course.description}</p>
              <div className='text-muted-foreground flex items-center space-x-4 text-sm'>
                <span>Code: {course.courseCode}</span>
                <span>•</span>
                <span>Instructor: {course.instructor}</span>
              </div>
            </div>
            <div className='text-right'>
              <div className='text-primary text-3xl font-bold'>
                {course.progress}%
              </div>
              <p className='text-muted-foreground text-sm'>Complete</p>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Progress value={course.progress} className='h-3' />
        </CardContent>
      </Card>

      {/* Course Modules */}
      <div className='space-y-6'>
        {course.modules.map((module, moduleIndex) => {
          const moduleStatus = getModuleStatus(module);

          return (
            <Card
              key={module.id}
              className={`${!module.isUnlocked ? 'opacity-60' : ''}`}
            >
              <CardHeader>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center space-x-3'>
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full ${
                        moduleStatus === 'completed'
                          ? 'bg-green-100 text-green-600'
                          : moduleStatus === 'quiz-pending'
                            ? 'bg-yellow-100 text-yellow-600'
                            : moduleStatus === 'locked'
                              ? 'bg-gray-100 text-gray-400'
                              : 'bg-blue-100 text-blue-600'
                      }`}
                    >
                      {moduleStatus === 'completed' ? (
                        <CheckCircle className='h-5 w-5' />
                      ) : moduleStatus === 'locked' ? (
                        <Lock className='h-5 w-5' />
                      ) : (
                        <BookOpen className='h-5 w-5' />
                      )}
                    </div>
                    <div>
                      <CardTitle className='text-lg'>
                        Module {moduleIndex + 1}: {module.name}
                      </CardTitle>
                      <CardDescription>{module.description}</CardDescription>
                    </div>
                  </div>
                  <div className='text-right'>
                    <div className='text-xl font-bold'>{module.progress}%</div>
                    <Progress value={module.progress} className='h-2 w-20' />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className='space-y-3'>
                  {module.chapters.map((chapter, chapterIndex) => {
                    const chapterStatus = getChapterStatus(chapter);

                    return (
                      <div
                        key={chapter.id}
                        className={`flex items-center justify-between rounded-lg border p-3 ${
                          !chapter.isUnlocked ? 'bg-gray-50' : ''
                        }`}
                      >
                        <div className='flex items-center space-x-3'>
                          <div
                            className={`flex h-8 w-8 items-center justify-center rounded-full ${
                              chapterStatus === 'completed'
                                ? 'bg-green-100 text-green-600'
                                : chapterStatus === 'quiz-pending'
                                  ? 'bg-yellow-100 text-yellow-600'
                                  : chapterStatus === 'locked'
                                    ? 'bg-gray-100 text-gray-400'
                                    : 'bg-blue-100 text-blue-600'
                            }`}
                          >
                            {chapterStatus === 'completed' ? (
                              <CheckCircle className='h-4 w-4' />
                            ) : chapterStatus === 'quiz-pending' ? (
                              <AlertCircle className='h-4 w-4' />
                            ) : chapterStatus === 'locked' ? (
                              <Lock className='h-4 w-4' />
                            ) : (
                              <BookOpen className='h-4 w-4' />
                            )}
                          </div>
                          <div>
                            <p className='font-medium'>{chapter.name}</p>
                            <p className='text-muted-foreground text-sm'>
                              {chapter.description}
                            </p>
                            {chapter.quizScore && (
                              <p className='text-xs text-green-600'>
                                Quiz Score: {chapter.quizScore}%
                              </p>
                            )}
                          </div>
                        </div>
                        <div className='flex items-center space-x-2'>
                          {chapter.isUnlocked && (
                            <>
                              <Button
                                variant='outline'
                                size='sm'
                                disabled={!chapter.isUnlocked}
                              >
                                <BookOpen className='mr-1 h-3 w-3' />
                                {chapter.isCompleted ? 'Review' : 'Study'}
                              </Button>
                              {chapter.hasQuiz && (
                                <Button
                                  size='sm'
                                  disabled={
                                    !chapter.isCompleted ||
                                    chapter.quizCompleted
                                  }
                                  variant={
                                    chapter.quizCompleted
                                      ? 'outline'
                                      : 'default'
                                  }
                                >
                                  <FileText className='mr-1 h-3 w-3' />
                                  {chapter.quizCompleted
                                    ? `Quiz: ${chapter.quizScore}%`
                                    : 'Take Quiz'}
                                </Button>
                              )}
                            </>
                          )}
                          {!chapter.isUnlocked && (
                            <Badge variant='outline'>
                              <Lock className='mr-1 h-3 w-3' />
                              Locked
                            </Badge>
                          )}
                        </div>
                      </div>
                    );
                  })}

                  {/* Module Quiz */}
                  {module.hasModuleQuiz && (
                    <div
                      className={`mt-4 rounded-lg border-2 border-dashed p-3 ${
                        moduleStatus === 'quiz-pending'
                          ? 'border-yellow-300 bg-yellow-50'
                          : module.moduleQuizCompleted
                            ? 'border-green-300 bg-green-50'
                            : 'border-gray-300 bg-gray-50'
                      }`}
                    >
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center space-x-2'>
                          <FileText
                            className={`h-4 w-4 ${
                              module.moduleQuizCompleted
                                ? 'text-green-600'
                                : moduleStatus === 'quiz-pending'
                                  ? 'text-yellow-600'
                                  : 'text-gray-400'
                            }`}
                          />
                          <div>
                            <span className='font-medium'>
                              Module Quiz: {module.name}
                            </span>
                            {module.moduleQuizScore && (
                              <span className='ml-2 text-sm text-green-600'>
                                Score: {module.moduleQuizScore}%
                              </span>
                            )}
                          </div>
                        </div>
                        <Button
                          size='sm'
                          disabled={moduleStatus !== 'quiz-pending'}
                          variant={
                            module.moduleQuizCompleted ? 'outline' : 'default'
                          }
                        >
                          {module.moduleQuizCompleted
                            ? 'Review Quiz'
                            : 'Take Module Quiz'}
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}

        {/* Final Exam */}
        {course.hasFinalExam && (
          <Card
            className={`border-2 ${
              course.finalExamUnlocked
                ? 'border-yellow-300 bg-yellow-50'
                : 'border-gray-300 bg-gray-50'
            }`}
          >
            <CardContent className='pt-6'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-3'>
                  <div
                    className={`flex h-12 w-12 items-center justify-center rounded-full ${
                      course.finalExamCompleted
                        ? 'bg-green-100 text-green-600'
                        : course.finalExamUnlocked
                          ? 'bg-yellow-100 text-yellow-600'
                          : 'bg-gray-100 text-gray-400'
                    }`}
                  >
                    {course.finalExamCompleted ? (
                      <CheckCircle className='h-6 w-6' />
                    ) : course.finalExamUnlocked ? (
                      <Award className='h-6 w-6' />
                    ) : (
                      <Lock className='h-6 w-6' />
                    )}
                  </div>
                  <div>
                    <h3 className='text-lg font-semibold'>Final Examination</h3>
                    <p className='text-muted-foreground text-sm'>
                      Comprehensive exam covering all course materials
                    </p>
                    {!course.finalExamUnlocked && (
                      <p className='mt-1 text-xs text-yellow-600'>
                        Complete all modules to unlock
                      </p>
                    )}
                  </div>
                </div>
                <Button
                  size='lg'
                  disabled={!course.finalExamUnlocked}
                  variant={course.finalExamCompleted ? 'outline' : 'default'}
                >
                  {course.finalExamCompleted ? (
                    <>
                      <Award className='mr-2 h-4 w-4' />
                      View Certificate
                    </>
                  ) : course.finalExamUnlocked ? (
                    <>
                      <Play className='mr-2 h-4 w-4' />
                      Take Final Exam
                    </>
                  ) : (
                    <>
                      <Lock className='mr-2 h-4 w-4' />
                      Locked
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
