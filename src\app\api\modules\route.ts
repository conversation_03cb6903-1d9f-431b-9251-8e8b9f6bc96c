import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { modules, courses, users, chapters } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/modules - Get modules for a course
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const courseId = searchParams.get('courseId');
    const teacherId = searchParams.get('teacherId');
    
    if (!courseId) {
      return NextResponse.json({ error: 'Course ID required' }, { status: 400 });
    }

    // Verify course exists and teacher has access
    if (teacherId) {
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, parseInt(courseId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Course not found or access denied' },
          { status: 403 }
        );
      }
    }

    // Get modules with chapter count
    const courseModules = await db
      .select()
      .from(modules)
      .where(eq(modules.courseId, parseInt(courseId)));

    const modulesWithChapterCount = await Promise.all(
      courseModules.map(async (module) => {
        const chapterCount = await db
          .select({ count: chapters.id })
          .from(chapters)
          .where(eq(chapters.moduleId, module.id));

        return {
          ...module,
          chapterCount: chapterCount.length
        };
      })
    );

    return NextResponse.json({ modules: modulesWithChapterCount });
  } catch (error) {
    console.error('Error fetching modules:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/modules - Create a new module
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      courseId,
      teacherId,
      orderIndex
    } = body;

    // Validate required fields
    if (!name || !courseId) {
      return NextResponse.json(
        { error: 'Name and course ID are required' },
        { status: 400 }
      );
    }

    // Verify course exists and teacher has access
    const course = await db
      .select()
      .from(courses)
      .where(eq(courses.id, courseId))
      .limit(1);

    if (course.length === 0) {
      return NextResponse.json({ error: 'Course not found' }, { status: 404 });
    }

    if (teacherId && course[0].teacherId !== teacherId) {
      return NextResponse.json(
        { error: 'Not authorized to add modules to this course' },
        { status: 403 }
      );
    }

    // If no orderIndex provided, set it to the next available
    let finalOrderIndex = orderIndex;
    if (finalOrderIndex === undefined) {
      const existingModules = await db
        .select({ orderIndex: modules.orderIndex })
        .from(modules)
        .where(eq(modules.courseId, courseId));
      
      finalOrderIndex = existingModules.length > 0 
        ? Math.max(...existingModules.map(m => m.orderIndex || 0)) + 1 
        : 1;
    }

    // Create the module
    const newModule = await db
      .insert(modules)
      .values({
        name,
        description,
        courseId,
        orderIndex: finalOrderIndex
      })
      .returning();

    return NextResponse.json(
      { module: newModule[0], message: 'Module created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating module:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}