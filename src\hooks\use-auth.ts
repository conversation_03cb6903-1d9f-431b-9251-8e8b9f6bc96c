'use client';

import { useState, useEffect } from 'react';
import { AuthUser } from '@/types/database';
import { authStorage } from '@/lib/auth';
import { useRouter } from 'next/navigation';

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    const storedUser = authStorage.getUser();
    if (storedUser) {
      setUser(storedUser);
    }
    setLoading(false);
  }, []);

  const signOut = () => {
    authStorage.removeUser();
    setUser(null);
    router.push('/auth/sign-in');
  };

  return { user, loading, signOut };
}
