'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ViewIcon as EyeIcon, ViewOffIcon as EyeOffIcon, Building02Icon as BuildingIcon, BookOpen01Icon as BookIcon, UserIcon, Mail01Icon as MailIcon, LockIcon, SmartPhone01Icon as PhoneIcon, ArrowLeft02Icon as ChevronLeftIcon, ArrowRight02Icon as ChevronRightIcon, Award01Icon as AwardIcon, Video01Icon as VideoIcon, Task01Icon as TaskIcon, UserGroupIcon as NetworkIcon } from 'hugeicons-react';
import Link from 'next/link';
import Image from 'next/image';

// Feature Carousel Component for Sign Up
const FeatureCarousel = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  
  const features = [
    {
      icon: VideoIcon,
      title: 'Akses Unlimited',
      description: 'Dapatkan akses tak terbatas ke seluruh koleksi kursus arsitektur terlengkap di Indonesia',
      highlight: 'Ribuan Jam Konten'
    },
    {
      icon: AwardIcon,
      title: 'Sertifikat Resmi IAI',
      description: 'Setiap kursus yang diselesaikan akan mendapatkan sertifikat resmi dari Ikatan Arsitek Indonesia',
      highlight: 'Diakui Industri'
    },
    {
      icon: NetworkIcon,
      title: 'Networking Profesional',
      description: 'Bergabung dengan komunitas lebih dari 10,000+ arsitek profesional di seluruh Indonesia',
      highlight: '10,000+ Anggota Aktif'
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % features.length);
    }, 4000);
    return () => clearInterval(timer);
  }, [features.length]);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % features.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + features.length) % features.length);
  };

  const currentFeature = features[currentSlide];
  const Icon = currentFeature.icon;

  return (
    <div className='bg-white/10 backdrop-blur-sm rounded-xl p-6 min-h-[200px]'>
      <div className='flex items-center justify-between mb-4'>
        <button
          onClick={prevSlide}
          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'
        >
          <ChevronLeftIcon className='w-4 h-4 text-white' />
        </button>
        
        <div className='flex space-x-2'>
          {features.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentSlide(index)}
              className={`w-2 h-2 rounded-full transition-colors ${
                index === currentSlide ? 'bg-white' : 'bg-white/40'
              }`}
            />
          ))}
        </div>
        
        <button
          onClick={nextSlide}
          className='p-2 rounded-full bg-white/10 hover:bg-white/20 transition-colors'
        >
          <ChevronRightIcon className='w-4 h-4 text-white' />
        </button>
      </div>

      <div className='text-center space-y-4'>
        <div className='flex justify-center'>
          <div className='w-16 h-16 bg-white/20 rounded-xl flex items-center justify-center'>
            <Icon className='w-8 h-8 text-white' />
          </div>
        </div>
        
        <div className='space-y-2'>
          <h3 className='text-xl font-bold text-white'>{currentFeature.title}</h3>
          <p className='text-white/90 text-sm leading-relaxed'>{currentFeature.description}</p>
          <div className='inline-block bg-white/20 px-3 py-1 rounded-full'>
            <span className='text-xs font-medium text-white'>{currentFeature.highlight}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function SignUpViewPage() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    iaiMembership: '',
    organization: '',
    password: '',
    confirmPassword: '',
    agreeTerms: false,
    subscribeUpdates: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    // Validate password confirmation
    if (formData.password !== formData.confirmPassword) {
      setError('Password tidak cocok');
      setLoading(false);
      return;
    }

    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phone: formData.phone,
          password: formData.password,
          iaiMembership: formData.iaiMembership,
          organization: formData.organization,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Registration failed');
      }

      toast.success('Registrasi berhasil!', {
        description: 'Akun Anda telah dibuat. Silakan login untuk melanjutkan.',
        duration: 4000,
      });
      
      // Reset form
      setFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        iaiMembership: '',
        organization: '',
        password: '',
        confirmPassword: '',
        agreeTerms: false,
        subscribeUpdates: false
      });
      
      // Optional: redirect to login page after a short delay
      setTimeout(() => {
        window.location.href = '/auth/sign-in';
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Terjadi kesalahan saat registrasi');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className='h-screen bg-gradient-to-br from-slate-50 to-blue-50 overflow-hidden'>
      <div className='flex h-screen'>
        {/* Left Side - Branding and Information */}
        <div className='hidden lg:flex lg:w-1/2 relative bg-gradient-to-br from-slate-800 to-slate-900 text-white'>
          {/* Background Pattern */}
          <div className='absolute inset-0 opacity-10'>
            <svg width="100%" height="100%" xmlns="http://www.w3.org/2000/svg">
              <defs>
                <pattern id="grid" width="60" height="60" patternUnits="userSpaceOnUse">
                  <path d="M 60 0 L 0 0 0 60" fill="none" stroke="currentColor" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>

          <div className='relative z-10 flex flex-col justify-between p-12 w-full'>
            {/* Logo and Branding */}
            <div className='space-y-4'>
              <div className='flex justify-center'>
                <Image
                  src='/assets/logo-iai-putih.png'
                  alt='IAI Logo'
                  width={200}
                  height={200}
                  className='object-contain'
                />
              </div>
              
              <div className='space-y-6 mt-12'>
                <h2 className='text-3xl font-bold leading-tight'>
                  Bergabunglah dengan<br />
                  Komunitas Arsitek<br />
                  Profesional Indonesia
                </h2>
                <p className='text-lg text-white/90 leading-relaxed'>
                  Daftarkan diri Anda dan mulai perjalanan pembelajaran profesional 
                  bersama ribuan arsitek Indonesia lainnya.
                </p>
              </div>

              {/* Features Carousel */}
              <div className='relative mt-8'>
                <FeatureCarousel />
              </div>
            </div>

          </div>
        </div>

        {/* Right Side - Registration Form */}
        <div className='flex-1 overflow-y-auto'>
          <div className='flex min-h-full items-start justify-center p-4 sm:p-8'>
            <div className='w-full max-w-md space-y-4 sm:space-y-6 py-4'>
            {/* Mobile Logo */}
            <div className='lg:hidden text-center mb-4 sm:mb-8'>
              <div className='flex justify-center mb-2 sm:mb-4'>
                <Image
                  src='/assets/logo-iai.png'
                  alt='IAI Logo'
                  width={150}
                  height={150}
                  className='object-contain sm:w-[200px] sm:h-[200px]'
                />
              </div>
            </div>

            {/* Registration Card */}
            <Card className='shadow-xl border-0'>
              <CardHeader className='text-center space-y-1 pb-4 sm:pb-6'>
                <CardTitle className='text-2xl font-bold text-gray-900'>Buat Akun Baru</CardTitle>
                <CardDescription className='text-gray-600'>
                  Lengkapi data diri untuk mendaftar sebagai anggota IAI LMS
                </CardDescription>
              </CardHeader>
              
              <CardContent>
                {error && (
                  <div className='mb-4 p-3 bg-red-50 border border-red-200 rounded-md'>
                    <p className='text-sm text-red-600'>{error}</p>
                  </div>
                )}

                <form onSubmit={handleSubmit} className='space-y-4'>
                  {/* Name Fields */}
                  <div className='grid grid-cols-2 gap-3'>
                    <div className='space-y-2'>
                      <Label htmlFor='firstName' className='text-sm font-medium text-gray-700'>
                        Nama Depan *
                      </Label>
                      <Input
                        id='firstName'
                        name='firstName'
                        type='text'
                        placeholder='Nama depan'
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                    </div>
                    <div className='space-y-2'>
                      <Label htmlFor='lastName' className='text-sm font-medium text-gray-700'>
                        Nama Belakang *
                      </Label>
                      <Input
                        id='lastName'
                        name='lastName'
                        type='text'
                        placeholder='Nama belakang'
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                    </div>
                  </div>

                  {/* Email Field */}
                  <div className='space-y-2'>
                    <Label htmlFor='email' className='text-sm font-medium text-gray-700'>
                      Email *
                    </Label>
                    <div className='relative'>
                      <MailIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
                      <Input
                        id='email'
                        name='email'
                        type='email'
                        placeholder='<EMAIL>'
                        value={formData.email}
                        onChange={handleInputChange}
                        className='pl-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                    </div>
                  </div>

                  {/* Phone Field */}
                  <div className='space-y-2'>
                    <Label htmlFor='phone' className='text-sm font-medium text-gray-700'>
                      Nomor Telepon *
                    </Label>
                    <div className='relative'>
                      <PhoneIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
                      <Input
                        id='phone'
                        name='phone'
                        type='tel'
                        placeholder='+62 812 3456 7890'
                        value={formData.phone}
                        onChange={handleInputChange}
                        className='pl-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                    </div>
                  </div>

                  {/* IAI Membership */}
                  <div className='space-y-2'>
                    <Label htmlFor='iaiMembership' className='text-sm font-medium text-gray-700'>
                      Status Keanggotaan IAI
                    </Label>
                    <Select onValueChange={(value) => handleSelectChange('iaiMembership', value)}>
                      <SelectTrigger className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'>
                        <SelectValue placeholder='Pilih status keanggotaan' />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='anggota-biasa'>Anggota Biasa</SelectItem>
                        <SelectItem value='anggota-muda'>Anggota Muda</SelectItem>
                        <SelectItem value='anggota-luar-biasa'>Anggota Luar Biasa</SelectItem>
                        <SelectItem value='belum-anggota'>Belum Menjadi Anggota</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Organization */}
                  <div className='space-y-2'>
                    <Label htmlFor='organization' className='text-sm font-medium text-gray-700'>
                      Instansi/Perusahaan
                    </Label>
                    <Input
                      id='organization'
                      name='organization'
                      type='text'
                      placeholder='Nama instansi atau perusahaan'
                      value={formData.organization}
                      onChange={handleInputChange}
                      className='h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                    />
                  </div>

                  {/* Password Fields */}
                  <div className='space-y-2'>
                    <Label htmlFor='password' className='text-sm font-medium text-gray-700'>
                      Password *
                    </Label>
                    <div className='relative'>
                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
                      <Input
                        id='password'
                        name='password'
                        type={showPassword ? 'text' : 'password'}
                        placeholder='Minimal 8 karakter'
                        value={formData.password}
                        onChange={handleInputChange}
                        className='pl-10 pr-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                      <button
                        type='button'
                        onClick={() => setShowPassword(!showPassword)}
                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                      >
                        {showPassword ? (
                          <EyeOffIcon className='w-4 h-4' />
                        ) : (
                          <EyeIcon className='w-4 h-4' />
                        )}
                      </button>
                    </div>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='confirmPassword' className='text-sm font-medium text-gray-700'>
                      Konfirmasi Password *
                    </Label>
                    <div className='relative'>
                      <LockIcon className='absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400' />
                      <Input
                        id='confirmPassword'
                        name='confirmPassword'
                        type={showConfirmPassword ? 'text' : 'password'}
                        placeholder='Ketik ulang password'
                        value={formData.confirmPassword}
                        onChange={handleInputChange}
                        className='pl-10 pr-10 h-11 border-gray-200 focus:border-[var(--iai-primary)] focus:ring-[var(--iai-primary)]'
                        required
                      />
                      <button
                        type='button'
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
                      >
                        {showConfirmPassword ? (
                          <EyeOffIcon className='w-4 h-4' />
                        ) : (
                          <EyeIcon className='w-4 h-4' />
                        )}
                      </button>
                    </div>
                  </div>

                  {/* Checkboxes */}
                  <div className='space-y-3 pt-2'>
                    <div className='flex items-start space-x-2'>
                      <Checkbox
                        id='agreeTerms'
                        name='agreeTerms'
                        checked={formData.agreeTerms}
                        onCheckedChange={(checked) => 
                          setFormData(prev => ({ ...prev, agreeTerms: checked as boolean }))
                        }
                        className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)] mt-0.5'
                        required
                      />
                      <Label 
                        htmlFor='agreeTerms' 
                        className='text-sm text-gray-600 cursor-pointer leading-relaxed'
                      >
                        Saya menyetujui{' '}
                        <Link
                          href='/terms'
                          className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium underline'
                        >
                          Syarat & Ketentuan
                        </Link>{' '}
                        dan{' '}
                        <Link
                          href='/privacy'
                          className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium underline'
                        >
                          Kebijakan Privasi
                        </Link>{' '}
                        IAI LMS *
                      </Label>
                    </div>

                    <div className='flex items-start space-x-2'>
                      <Checkbox
                        id='subscribeUpdates'
                        name='subscribeUpdates'
                        checked={formData.subscribeUpdates}
                        onCheckedChange={(checked) => 
                          setFormData(prev => ({ ...prev, subscribeUpdates: checked as boolean }))
                        }
                        className='data-[state=checked]:bg-[var(--iai-primary)] data-[state=checked]:border-[var(--iai-primary)] mt-0.5'
                      />
                      <Label 
                        htmlFor='subscribeUpdates' 
                        className='text-sm text-gray-600 cursor-pointer leading-relaxed'
                      >
                        Saya ingin menerima informasi kursus baru dan update terbaru dari IAI LMS
                      </Label>
                    </div>
                  </div>

                  {/* Register Button */}
                  <Button
                    type='submit'
                    variant='iai'
                    className='w-full h-12 text-base font-medium mt-6'
                    disabled={!formData.agreeTerms || loading}
                  >
                    {loading ? 'Mendaftar...' : 'Daftar Sekarang'}
                  </Button>
                </form>

                {/* Sign In Link */}
                <p className='text-center text-sm text-gray-600 mt-6'>
                  Sudah punya akun?{' '}
                  <Link
                    href='/auth/sign-in'
                    className='text-[var(--iai-primary)] hover:text-[var(--iai-secondary)] font-medium'
                  >
                    Masuk di sini
                  </Link>
                </p>
              </CardContent>
            </Card>


            {/* Footer */}
            <div className='text-center text-xs text-gray-500 space-y-1'>
              <div className='flex items-center justify-center space-x-2 text-gray-400'>
                <span>© 2024 IAI LMS - Powered by</span>
                <img
                  src="https://cdn.terang.ai/images/logo/logo-terang-ai.svg"
                  alt="Terang AI"
                  className='h-4 inline-block'
                />
              </div>
            </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}