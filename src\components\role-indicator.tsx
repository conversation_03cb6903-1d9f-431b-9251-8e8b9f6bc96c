'use client';

import { GraduationCap, BookOpen, Building2 } from 'lucide-react';
import * as React from 'react';
import { authStorage } from '@/lib/auth';
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem
} from '@/components/ui/sidebar';

const getRoleDisplay = (role: string) => {
  switch (role) {
    case 'teacher':
      return {
        label: 'Teacher',
        icon: GraduationCap,
        bgColor: 'bg-blue-600'
      };
    case 'student':
      return {
        label: 'Student',
        icon: BookOpen,
        bgColor: 'bg-green-600'
      };
    case 'super_admin':
      return {
        label: 'Institution Manager',
        icon: Building2,
        bgColor: 'bg-purple-600'
      };
    default:
      return {
        label: 'User',
        icon: BookOpen,
        bgColor: 'bg-gray-600'
      };
  }
};

export function RoleIndicator() {
  const [user, setUser] = React.useState(authStorage.getUser());

  React.useEffect(() => {
    const currentUser = authStorage.getUser();
    setUser(currentUser);
  }, []);

  if (!user) {
    return null;
  }

  const roleInfo = getRoleDisplay(user.role);
  const Icon = roleInfo.icon;

  return (
    <SidebarMenu>
      <SidebarMenuItem>
        <SidebarMenuButton
          size='lg'
          className='cursor-default hover:bg-transparent'
        >
          <div className={`${roleInfo.bgColor} text-white flex aspect-square size-8 items-center justify-center rounded-lg`}>
            <Icon className='size-4' />
          </div>
          <div className='flex flex-col gap-0.5 leading-none'>
            <span className='font-semibold'>{user.name}</span>
            <span className='text-sm text-muted-foreground'>Account Type: {roleInfo.label}</span>
          </div>
        </SidebarMenuButton>
      </SidebarMenuItem>
    </SidebarMenu>
  );
}