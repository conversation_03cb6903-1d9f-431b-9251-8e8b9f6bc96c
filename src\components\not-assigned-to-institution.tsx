'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, Building2, Mail } from 'lucide-react';
import { authStorage } from '@/lib/auth';
import PageContainer from '@/components/layout/page-container';

interface NotAssignedToInstitutionProps {
  userRole: 'teacher' | 'student';
}

export default function NotAssignedToInstitution({ userRole }: NotAssignedToInstitutionProps) {
  const user = authStorage.getUser();
  
  const handleLogout = () => {
    authStorage.removeUser();
    window.location.href = '/auth/sign-in';
  };

  const getRoleSpecificMessage = () => {
    if (userRole === 'teacher') {
      return {
        title: 'Akun Teacher Belum Terdaftar di Institusi',
        description: 'Akun Anda sebagai Teacher belum ditugaskan ke institusi manapun. Silakan hubungi Super Admin untuk mendapatkan akses ke institusi.',
        actionText: 'Hubungi Super Admin'
      };
    } else {
      return {
        title: 'Akun Student Belum Terdaftar di Institusi',
        description: 'Akun Anda sebagai Student belum ditugaskan ke institusi manapun. Silakan hubungi Teacher atau Super Admin untuk mendapatkan akses ke institusi.',
        actionText: 'Hubungi Admin'
      };
    }
  };

  const { title, description, actionText } = getRoleSpecificMessage();

  return (
    <PageContainer>
      <div className='flex w-full min-h-[calc(100vh-200px)] items-center justify-center'>
        <Card className='w-full max-w-md mx-auto'>
        <CardHeader className='text-center'>
          <div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-orange-100'>
            <AlertCircle className='h-8 w-8 text-orange-600' />
          </div>
          <CardTitle className='text-xl font-semibold'>{title}</CardTitle>
          <CardDescription className='text-sm text-muted-foreground'>
            {description}
          </CardDescription>
        </CardHeader>
        <CardContent className='space-y-4'>
          <div className='rounded-lg border bg-muted/50 p-4'>
            <div className='flex items-center space-x-3'>
              <Building2 className='h-5 w-5 text-muted-foreground' />
              <div>
                <p className='text-sm font-medium'>Status Institusi</p>
                <p className='text-xs text-muted-foreground'>Belum ditugaskan</p>
              </div>
            </div>
          </div>
          
          <div className='rounded-lg border bg-muted/50 p-4'>
            <div className='flex items-center space-x-3'>
              <Mail className='h-5 w-5 text-muted-foreground' />
              <div>
                <p className='text-sm font-medium'>Email Akun</p>
                <p className='text-xs text-muted-foreground'>{user?.email}</p>
              </div>
            </div>
          </div>

          <div className='space-y-2'>
            <p className='text-xs text-muted-foreground text-center'>
              Setelah Super Admin menugaskan Anda ke institusi, silakan logout dan login kembali untuk mengakses dashboard.
            </p>
          </div>

          <div className='flex flex-col space-y-2'>
            <Button variant='outline' className='w-full'>
              <Mail className='mr-2 h-4 w-4' />
              {actionText}
            </Button>
            <Button variant='ghost' onClick={handleLogout} className='w-full'>
              Logout
            </Button>
          </div>
        </CardContent>
        </Card>
      </div>
    </PageContainer>
  );
}