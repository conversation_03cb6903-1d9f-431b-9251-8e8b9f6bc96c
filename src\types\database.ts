import { InferSelectModel, InferInsertModel } from 'drizzle-orm';
import {
  users,
  institutions,
  classes,
  courses,
  modules,
  chapters,
  quizzes,
  questions,
  quizAttempts,
  studentEnrollments,
  courseEnrollments,
  studentProgress
} from '@/lib/db/schema';

// User types
export type User = InferSelectModel<typeof users>;
export type NewUser = InferInsertModel<typeof users>;

// Institution types
export type Institution = InferSelectModel<typeof institutions>;
export type NewInstitution = InferInsertModel<typeof institutions>;

// Class types
export type Class = InferSelectModel<typeof classes>;
export type NewClass = InferInsertModel<typeof classes>;

// Course types
export type Course = InferSelectModel<typeof courses>;
export type NewCourse = InferInsertModel<typeof courses>;

// Module types
export type Module = InferSelectModel<typeof modules>;
export type NewModule = InferInsertModel<typeof modules>;

// Chapter types
export type Chapter = InferSelectModel<typeof chapters>;
export type NewChapter = InferInsertModel<typeof chapters>;

// Quiz types
export type Quiz = InferSelectModel<typeof quizzes>;
export type NewQuiz = InferInsertModel<typeof quizzes>;

// Question types
export type Question = InferSelectModel<typeof questions>;
export type NewQuestion = InferInsertModel<typeof questions>;

// Quiz attempt types
export type QuizAttempt = InferSelectModel<typeof quizAttempts>;
export type NewQuizAttempt = InferInsertModel<typeof quizAttempts>;

// Student enrollment types
export type StudentEnrollment = InferSelectModel<typeof studentEnrollments>;
export type NewStudentEnrollment = InferInsertModel<typeof studentEnrollments>;

// Course enrollment types
export type CourseEnrollment = InferSelectModel<typeof courseEnrollments>;
export type NewCourseEnrollment = InferInsertModel<typeof courseEnrollments>;

// Student progress types
export type StudentProgress = InferSelectModel<typeof studentProgress>;
export type NewStudentProgress = InferInsertModel<typeof studentProgress>;

// Subscription plan types
export type SubscriptionPlan = {
  name: string;
  pricePerStudent: {
    monthly: number;
    yearly: number;
  };
  features: string[];
  minStudents: number;
  maxStudents: number;
  isPopular: boolean;
  idealFor: string;
  icon: React.ElementType;
  description: string;
};

// Extended types with relations
export type UserWithInstitution = User & {
  institution?: Institution;
};

export type CourseWithDetails = Course & {
  teacher: User;
  institution: Institution;
  modules: Module[];
  studentEnrollments: StudentEnrollment[];
};

export type ModuleWithChapters = Module & {
  chapters: Chapter[];
  quizzes: Quiz[];
};

export type ChapterWithQuiz = Chapter & {
  quizzes: Quiz[];
};

export type QuizWithQuestions = Quiz & {
  questions: Question[];
};

export type ClassWithDetails = Class & {
  teacher: User;
  institution: Institution;
  courseEnrollments: (CourseEnrollment & { course: Course })[];
};

export type InstitutionWithDetails = Institution & {
  users: User[];
  classes: Class[];
  courses: Course[];
};

// API Response types
export type ApiResponse<T = any> = {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
};

// Auth types
export type AuthUser = {
  id: number;
  name: string;
  email: string;
  role: 'student' | 'teacher' | 'super_admin';
  institutionId?: number;
};

export type LoginCredentials = {
  email: string;
  password: string;
};

export type RegisterData = {
  name: string;
  email: string;
  password: string;
  role: 'student' | 'teacher' | 'super_admin';
  institutionId?: number;
};

// Course generation types
export type CourseOutline = {
  courseName: string;
  description: string;
  modules: {
    name: string;
    description: string;
    chapters: {
      name: string;
      description: string;
      hasQuiz: boolean;
    }[];
    hasModuleQuiz: boolean;
  }[];
  hasFinalExam: boolean;
};

export type GeneratedQuiz = {
  name: string;
  description: string;
  timeLimit: number;
  minimumScore: number;
  questions: {
    type: 'multiple_choice' | 'true_false' | 'essay';
    question: string;
    options?: string[];
    correctAnswer: string;
    points: number;
  }[];
};

export type GeneratedContent = {
  content: string;
  quiz?: GeneratedQuiz;
};
