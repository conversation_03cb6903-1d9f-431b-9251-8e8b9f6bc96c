'use client';

import { useState, useEffect } from "react";
import { Course } from "@/types/lms";
import { architectureCourse } from "@/constants/shared-course-data";
import LandingNavbar from "./LandingNavbar";
import CourseHeroSection from "./CourseHeroSection";
import CourseGrid from "./CourseGrid";
import FeaturesSection from "./FeaturesSection";
import CallToActionSection from "./CallToActionSection";
import Footer from "./Footer";
import { useRouter } from 'next/navigation';

// Enhanced course data with purchase functionality (same as student courses page)
const enhancedArchitectureCourse: Course = {
  ...architectureCourse,
  isPurchasable: true,
  price: 150000,
  currency: 'IDR',
  previewMode: true,
  thumbnail: '/assets/architecture.png',
  admissions: {
    requirements: [
      'Gelar Sarjana Arsitektur (S1) atau sederajat',
      'Pengalaman kerja minimal 2 tahun di bidang arsitektur',
      'Portofolio proyek arsitektur',
      'Surat rekomendasi dari atasan/mentor profesional'
    ],
    applicationDeadline: '2024-12-31',
    prerequisites: [
      'Penguasaan AutoCAD dan/atau software BIM',
      '<PERSON><PERSON><PERSON> standar SNI dan peraturan bangunan Indonesia',
      'Pengalaman dalam perencanaan dan desain arsitektur',
      'Kemampuan membaca dan membuat gambar teknik'
    ]
  },
  academics: {
    credits: 12,
    workload: '12-15 jam/minggu',
    assessment: [
      'Ujian teori (25%)',
      'Studi kasus proyek (35%)',
      'Presentasi portofolio (40%)'
    ]
  },
  tuitionAndFinancing: {
    totalCost: 6000000,
    paymentOptions: [
      'Pembayaran penuh (diskon 5%)',
      'Cicilan bulanan (3 bulan)',
      'Paket pembayaran mahasiswa tersedia'
    ],
    scholarships: [
      'Beasiswa berbasis prestasi (hingga 50% off)',
      'Diskon early bird (15% off)',
      'Diskon pendaftaran grup (10% off untuk 3+ siswa)'
    ]
  },
  careers: {
    outcomes: [
      'Sertifikasi Arsitek Profesional Indonesia (IAI)',
      'Kompetensi perencanaan bangunan tinggi',
      'Penguasaan regulasi dan standar konstruksi',
      'Kemampuan review dan supervisi proyek'
    ],
    industries: [
      'Firma Arsitektur',
      'Perusahaan Konstruksi',
      'Perencanaan Kota',
      'Desain Interior',
      'Pengembangan Real Estat'
    ],
    averageSalary: 'Rp780.000.000 - Rp1.140.000.000 per tahun'
  },
  studentExperience: {
    testimonials: [
      {
        name: 'Sarah Martinez',
        feedback: 'Program sertifikasi ini sangat membantu saya meningkatkan kompetensi profesional. Sekarang saya bisa menangani proyek yang lebih kompleks dengan percaya diri.'
      },
      {
        name: 'David Chen',
        feedback: 'Materi yang up-to-date dengan regulasi terbaru dan studi kasus yang relevan dengan kondisi Indonesia. Sangat recommended untuk arsitek yang ingin upgrade skill.'
      },
      {
        name: 'Maria Rodriguez',
        feedback: 'Dengan sertifikat IAI ini, saya bisa mengajukan izin praktik mandiri. ROI yang sangat baik untuk investasi pengembangan karier profesional.'
      }
    ],
    facilities: [
      'Platform pembelajaran online',
      'Studio desain virtual',
      'Akses perpustakaan digital',
      'Dukungan teknis 24/7',
      'Akses aplikasi mobile'
    ],
    support: [
      'Instruktur kursus khusus',
      'Forum diskusi rekan',
      'Jam kantor mingguan',
      'Layanan konseling karier',
      'Akses jaringan alumni'
    ]
  }
};

// Create additional mock courses
const engineeringCourse: Course = {
  ...enhancedArchitectureCourse,
  id: 'eng-cert-001',
  name: 'Sertifikasi Teknik Sipil Profesional',
  code: 'ENG-CERT-001',
  description: 'Program sertifikasi untuk insinyur sipil yang ingin meningkatkan kompetensi di bidang konstruksi dan infrastruktur',
  instructor: 'Ir. Bambang Prasetyo, MT',
  enrollmentCode: 'ENG-CERT-2024',
  price: 180000,
  careers: {
    ...enhancedArchitectureCourse.careers,
    outcomes: [
      'Sertifikasi Insinyur Sipil Profesional',
      'Kompetensi analisis struktur bangunan',
      'Penguasaan standar konstruksi Indonesia',
      'Kemampuan manajemen proyek konstruksi'
    ],
    industries: [
      'Perusahaan Konstruksi',
      'Konsultan Struktur',
      'Perencanaan Infrastruktur',
      'Pengembangan Real Estate',
      'Pemerintahan Daerah'
    ],
    averageSalary: 'Rp650.000.000 - Rp950.000.000 per tahun'
  }
};

const planningCourse: Course = {
  ...enhancedArchitectureCourse,
  id: 'plan-cert-001',
  name: 'Sertifikasi Perencana Wilayah dan Kota',
  code: 'PLAN-CERT-001', 
  description: 'Program sertifikasi untuk perencana dalam bidang tata ruang wilayah dan perkotaan',
  instructor: 'Dr. Siti Nurhaliza, ST, MT',
  enrollmentCode: 'PLAN-CERT-2024',
  price: 160000,
  careers: {
    ...enhancedArchitectureCourse.careers,
    outcomes: [
      'Sertifikasi Perencana Wilayah Profesional',
      'Kompetensi analisis tata ruang',
      'Penguasaan regulasi perencanaan kota',
      'Kemampuan GIS dan mapping'
    ],
    industries: [
      'Bappeda',
      'Konsultan Perencanaan',
      'Developer Property',
      'NGO Lingkungan',
      'Universitas'
    ],
    averageSalary: 'Rp580.000.000 - Rp850.000.000 per tahun'
  }
};

const mockCourses: Course[] = [enhancedArchitectureCourse, engineeringCourse, planningCourse];

export default function CourseLandingPage() {
  const [courses, setCourses] = useState<Course[]>([]);
  const [enrolledCourses, setEnrolledCourses] = useState<string[]>([]);
  const router = useRouter();

  // Fetch courses when component mounts
  useEffect(() => {
    fetchCourses();
    fetchEnrolledCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      // Use the same mock courses that the student courses page uses
      console.log('Setting courses:', mockCourses);
      setCourses(mockCourses);
    } catch (err) {
      console.error('Error setting courses:', err);
      // Fallback to basic architecture course
      setCourses([architectureCourse]);
    }
  };

  const fetchEnrolledCourses = async () => {
    try {
      // Check if user is enrolled in any courses
      // const response = await fetch('/api/user/enrolled-courses');
      // if (response.ok) {
      //   const data = await response.json();
      //   setEnrolledCourses(data.map(course => course.id));
      // }
      
      // For demo purposes, assume no enrollments initially
      setEnrolledCourses([]);
    } catch (err) {
      console.error('Error fetching enrolled courses:', err);
    }
  };

  const handleEnrollCourse = (courseId: string) => {
    // Check if user is authenticated
    const isAuthenticated = checkAuthStatus();
    
    if (!isAuthenticated) {
      // Redirect to sign in with course enrollment intent
      router.push(`/auth/sign-in?enrollCourse=${courseId}`);
      return;
    }

    // If authenticated, redirect to course enrollment page or directly to course
    if (enrolledCourses.includes(courseId)) {
      // Already enrolled, go to course
      router.push(`/my-courses/${courseId}`);
    } else {
      // Enroll in course
      router.push(`/courses/${courseId}/enroll`);
    }
  };

  const handlePreviewCourse = (courseId: string) => {
    // Redirect to login page when user clicks "Lihat Detail"
    router.push('/auth/sign-in');
  };

  const checkAuthStatus = () => {
    // Check if user is authenticated - replace with actual auth check
    try {
      const token = localStorage.getItem('authToken');
      return !!token;
    } catch {
      return false;
    }
  };

  const scrollToCourses = () => {
    const coursesSection = document.getElementById('courses');
    if (coursesSection) {
      coursesSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <div className="flex flex-col min-h-screen">
      <LandingNavbar />
      <main className="flex-1 pt-20">
        <section id="home">
          <CourseHeroSection onCTA={scrollToCourses} />
        </section>
        <CourseGrid 
          courses={courses}
          onEnroll={handleEnrollCourse}
          onPreview={handlePreviewCourse}
          enrolledCourses={enrolledCourses}
        />
        <section id="features">
          <FeaturesSection />
        </section>
        <section id="about">
          <CallToActionSection onCTA={scrollToCourses} />
        </section>
      </main>
      <Footer />
    </div>
  );
}