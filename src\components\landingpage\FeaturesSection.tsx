'use client';

import {
  BookOpen01Icon as BookOpenIcon,
  Award01Icon as AwardIcon,
  Globe02Icon as GlobeIcon,
  UserGroupIcon,
  Time01Icon as TimeIcon,
  CheckmarkCircle01Icon as CheckIcon
} from 'hugeicons-react';

const features = [
  {
    icon: BookOpenIcon,
    title: "Pembelajaran Komprehensif",
    description: "Akses ke modul kursus lengkap dengan konten interaktif, video, dan latihan praktis."
  },
  {
    icon: AwardIcon,
    title: "Sertifikasi Profesional",
    description: "Dapatkan sertifikat yang diakui dari Ikatan Arsitek Indonesia (IAI) setelah menyelesaikan kursus."
  },
  {
    icon: GlobeIcon,
    title: "Akses Online",
    description: "Belajar dengan kecepatan Anda sendiri dari mana saja, dengan akses 24/7 ke materi dan sumber daya kursus."
  },
  {
    icon: UserGroupIcon,
    title: "Instruktur Ahli",
    description: "Belajar dari profesional industri dan arsitek bersertifikat dengan pengalaman praktis bertahun-tahun."
  },
  {
    icon: TimeIcon,
    title: "Jadwal Fleksibel",
    description: "Pembelajaran mandiri yang sesuai dengan jadwal sibuk Anda, dengan akses seumur hidup ke kursus yang terdaftar."
  },
  {
    icon: CheckIcon,
    title: "Pelacakan Kemajuan",
    description: "Pantau kemajuan pembelajaran Anda dengan analitik terperinci dan pencapaian milestone."
  }
];

export default function FeaturesSection() {
  return (
    <section className="py-16 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Mengapa Memilih Platform Kami?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Kami menyediakan pengalaman pembelajaran yang paling komprehensif dan profesional untuk sertifikasi arsitektur dan kemajuan karir.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl mb-6 group-hover:bg-blue-200 transition-colors duration-200">
                    <IconComponent className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-4">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {feature.description}
                  </p>
                </div>
              );
            })}
          </div>

          {/* Stats Section */}
          <div className="mt-20 bg-gray-50 rounded-3xl p-8 lg:p-12">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
              <div>
                <div className="text-4xl font-bold text-blue-600 mb-2">1000+</div>
                <div className="text-gray-600">Siswa Terdaftar</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-green-600 mb-2">95%</div>
                <div className="text-gray-600">Tingkat Penyelesaian</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-purple-600 mb-2">50+</div>
                <div className="text-gray-600">Instruktur Ahli</div>
              </div>
              <div>
                <div className="text-4xl font-bold text-orange-600 mb-2">24/7</div>
                <div className="text-gray-600">Dukungan Tersedia</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}