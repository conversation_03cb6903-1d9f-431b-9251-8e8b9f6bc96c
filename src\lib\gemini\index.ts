// Export types
export * from './types';

// Export client utilities
export {
  getGeminiClient,
  fileToBase64
} from './client';

// Export course outline generation
export {
  generateCourseOutlineFromPDF,
  generateCourseOutlineFromText,
  createCourseOutlinePrompt,
  validateCourseOutline
} from './course-outline';

// Export content generation
export {
  generateChapterContent,
  createChapterContentPrompt,
  validateGeneratedContent
} from './content-generator';

// Export quiz generation
export {
  generateModuleQuiz,
  generateFinalExam,
  generateChapterQuiz,
  createModuleQuizPrompt,
  createFinalExamPrompt,
  createChapterQuizPrompt,
  validateGeneratedQuiz
} from './quiz-generator';

// Export batch generation
export {
  BatchContentGenerator,
  generateCourseContentBatch,
  generateChaptersBatch,
  estimateGenerationTime,
  type BatchGenerationResults
} from './batch-generator';

// Re-export commonly used types for convenience
export type {
  AICourseOutline,
  AIGeneratedContent,
  AIGeneratedQuiz,
  GenerationProgress,
  GenerationStep,
  AIGenerationConfig,
} from './types';
export { AIGenerationError } from './types';