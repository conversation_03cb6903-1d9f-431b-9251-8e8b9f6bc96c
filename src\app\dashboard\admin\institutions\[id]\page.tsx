'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { institutionTypes, subscriptionPlans } from '@/config/subscriptions';
import { useToast } from '@/hooks/use-toast';

interface Institution {
  id: number;
  name: string;
  type: string;
  subscription_plan: string;
  billing_cycle: string;
  payment_status: string;
  payment_due_date: string;
  student_count: number;
  teacher_count: number;
  created_at: string;
  updated_at: string;
}

export default function EditInstitutionPage() {
  const router = useRouter();
  const params = useParams();
  const institutionId = params.id as string;
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [institution, setInstitution] = useState<Institution | null>(null);
  const [dataLoaded, setDataLoaded] = useState(false); // Add this flag
  const [formData, setFormData] = useState({
    name: '',
    type: '',
    subscriptionPlan: 'basic',
    billingCycle: 'monthly',
    studentCount: 0,
    teacherCount: 0,
    paymentStatus: 'unpaid',
    paymentDueDate: ''
  });

  // Fetch institution data - only run once
  useEffect(() => {
    if (!institutionId || dataLoaded) return; // Prevent re-fetching if data already loaded

    const fetchInstitution = async () => {
      try {
        const response = await fetch(`/api/institutions/${institutionId}`);
        const data = await response.json();

        if (data.success) {
          const inst = data.data;
          setInstitution(inst);

          // Only set form data if it hasn't been loaded yet
          if (!dataLoaded) {
            setFormData({
              name: inst.name,
              type: inst.type,
              subscriptionPlan: inst.subscription_plan,
              billingCycle: inst.billing_cycle,
              studentCount: inst.student_count,
              teacherCount: inst.teacher_count,
              paymentStatus: inst.payment_status,
              paymentDueDate: inst.payment_due_date
                ? new Date(inst.payment_due_date).toISOString().split('T')[0]
                : ''
            });
            setDataLoaded(true); // Mark data as loaded
          }
        } else {
          toast({
            title: 'Error',
            description: data.error || 'Failed to fetch institution',
            variant: 'destructive'
          });
          router.push('/dashboard/admin/institutions');
        }
      } catch (error) {
        console.error('Error fetching institution:', error);
        toast({
          title: 'Error',
          description: 'Failed to fetch institution',
          variant: 'destructive'
        });
        router.push('/dashboard/admin/institutions');
      } finally {
        setLoading(false);
      }
    };

    fetchInstitution();
  }, [institutionId, dataLoaded, router, toast]); // Add dataLoaded to dependencies

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await fetch(`/api/institutions/${institutionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name,
          type: formData.type,
          subscriptionPlan: formData.subscriptionPlan,
          billingCycle: formData.billingCycle,
          studentCount: formData.studentCount,
          teacherCount: formData.teacherCount,
          paymentStatus: formData.paymentStatus,
          paymentDueDate: formData.paymentDueDate
            ? new Date(formData.paymentDueDate).toISOString()
            : null
        })
      });

      const data = await response.json();

      if (data.success) {
        toast({
          title: 'Success',
          description: 'Institution updated successfully'
        });
        router.push('/dashboard/admin/institutions');
      } else {
        toast({
          title: 'Error',
          description: data.error || 'Failed to update institution',
          variant: 'destructive'
        });
      }
    } catch (error) {
      console.error('Error updating institution:', error);
      toast({
        title: 'Error',
        description: 'Failed to update institution',
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <div className='flex min-h-[400px] items-center justify-center'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <p className='ml-2'>Loading institution...</p>
      </div>
    );
  }

  if (!institution) {
    return (
      <div className='py-8 text-center'>
        <p>Institution not found</p>
        <Link href='/dashboard/admin/institutions'>
          <Button className='mt-4'>Back to Institutions</Button>
        </Link>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/admin/institutions'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Edit Institution
          </h1>
          <p className='text-muted-foreground'>
            Update institution information
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Institution Details</CardTitle>
          <CardDescription>
            Update the information for {institution.name}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='grid grid-cols-1 gap-6 md:grid-cols-2'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Institution Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder='Enter institution name'
                  required
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='type'>Institution Type</Label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange('type', value)}
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select institution type' />
                  </SelectTrigger>
                  <SelectContent>
                    {institutionTypes.map((type) => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='subscriptionPlan'>Subscription Plan</Label>
                <Select
                  value={formData.subscriptionPlan}
                  onValueChange={(value) =>
                    handleInputChange('subscriptionPlan', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(subscriptionPlans).map(([key, plan]) => (
                      <SelectItem key={key} value={key}>
                        {plan.name} - Rp{' '}
                        {plan.pricePerStudent.monthly.toLocaleString()}
                        /student/month
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='billingCycle'>Billing Cycle</Label>
                <Select
                  value={formData.billingCycle}
                  onValueChange={(value) =>
                    handleInputChange('billingCycle', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='monthly'>Monthly</SelectItem>
                    <SelectItem value='yearly'>
                      Yearly (25% discount)
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='studentCount'>Number of Students</Label>
                <Input
                  id='studentCount'
                  type='number'
                  value={formData.studentCount}
                  onChange={(e) =>
                    handleInputChange(
                      'studentCount',
                      parseInt(e.target.value) || 0
                    )
                  }
                  placeholder='0'
                  min='0'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='teacherCount'>Number of Teachers</Label>
                <Input
                  id='teacherCount'
                  type='number'
                  value={formData.teacherCount}
                  onChange={(e) =>
                    handleInputChange(
                      'teacherCount',
                      parseInt(e.target.value) || 0
                    )
                  }
                  placeholder='0'
                  min='0'
                />
              </div>

              <div className='space-y-2'>
                <Label htmlFor='paymentStatus'>Payment Status</Label>
                <Select
                  value={formData.paymentStatus}
                  onValueChange={(value) =>
                    handleInputChange('paymentStatus', value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value='paid'>Paid</SelectItem>
                    <SelectItem value='unpaid'>Unpaid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='paymentDueDate'>Payment Due Date</Label>
                <Input
                  id='paymentDueDate'
                  type='date'
                  value={formData.paymentDueDate}
                  onChange={(e) =>
                    handleInputChange('paymentDueDate', e.target.value)
                  }
                />
              </div>
            </div>

            <div className='flex justify-end space-x-4'>
              <Link href='/dashboard/admin/institutions'>
                <Button variant='outline' type='button'>
                  Cancel
                </Button>
              </Link>
              <Button type='submit' disabled={isLoading}>
                <Save className='mr-2 h-4 w-4' />
                {isLoading ? 'Updating...' : 'Update Institution'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
