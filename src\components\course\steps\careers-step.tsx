import React from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Briefcase, Building, DollarSign } from 'lucide-react';
import { CourseData, CareersData } from '../course-creation-wizard';

interface CareersStepProps {
  data: CourseData;
  onUpdate: (updates: Partial<CourseData>) => void;
}

export function CareersStep({ data, onUpdate }: CareersStepProps) {
  const careers = data.careers || { outcomes: [], industries: [], averageSalary: '' };

  const handleUpdate = (field: keyof CareersData, value: string | string[]) => {
    onUpdate({
      careers: {
        ...careers,
        [field]: value,
      },
    });
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle><PERSON><PERSON><PERSON></CardTitle>
        <CardDescription>Detail terkait hasil karir dan industri yang relevan setelah kursus.</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center space-x-2">
          <Briefcase className="h-5 w-5 text-gray-500" />
          <Label htmlFor="outcomes">Hasil (pisahkan dengan koma)</Label>
        </div>
        <Textarea
          id="outcomes"
          value={careers.outcomes.join(', ')}
          onChange={(e) => handleUpdate('outcomes', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Sertifikasi Arsitek Profesional Indonesia (IAI), Kompetensi perencanaan bangunan tinggi"
        />

        <div className="flex items-center space-x-2">
          <Building className="h-5 w-5 text-gray-500" />
          <Label htmlFor="industries">Industri (pisahkan dengan koma)</Label>
        </div>
        <Textarea
          id="industries"
          value={careers.industries.join(', ')}
          onChange={(e) => handleUpdate('industries', e.target.value.split(',').map(s => s.trim()))}
          placeholder="Contoh: Firma Arsitektur, Perusahaan Konstruksi"
        />

        <div className="flex items-center space-x-2">
          <DollarSign className="h-5 w-5 text-gray-500" />
          <Label htmlFor="averageSalary">Rata-rata Gaji</Label>
        </div>
        <Input
          id="averageSalary"
          type="text"
          value={careers.averageSalary}
          onChange={(e) => handleUpdate('averageSalary', e.target.value)}
          placeholder="Contoh: Rp780.000.000 - Rp1.140.000.000 per tahun"
        />
      </CardContent>
    </Card>
  );
}